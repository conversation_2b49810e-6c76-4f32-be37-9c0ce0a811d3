/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,jsx}",
    "./App.jsx",
    "./popup.jsx",
    "./types.js",
  ],
  safelist: [
    // Ensure all color classes are included
    'bg-slate-900', 'bg-slate-800', 'bg-slate-700', 'bg-slate-600', 'bg-slate-500',
    'text-slate-100', 'text-slate-200', 'text-slate-300', 'text-slate-400', 'text-slate-500',
    'border-slate-700', 'border-slate-600', 'border-slate-500',
    'bg-green-600', 'bg-green-500', 'hover:bg-green-500', 'hover:bg-green-400',
    'bg-red-600', 'bg-red-500', 'hover:bg-red-500', 'hover:bg-red-400',
    'bg-yellow-500', 'bg-yellow-400', 'hover:bg-yellow-400', 'hover:bg-yellow-300',
    'bg-orange-500', 'bg-orange-400', 'hover:bg-orange-400', 'hover:bg-orange-300',
    'bg-indigo-600', 'bg-indigo-500', 'hover:bg-indigo-500', 'hover:bg-indigo-400',
    'text-indigo-400', 'text-purple-500', 'text-green-400', 'text-red-400',
    'text-blue-400', 'text-blue-300', 'text-red-300', 'text-green-300',
    'text-purple-300', 'text-teal-300', 'text-teal-400',
    'bg-red-700/30', 'border-red-500', 'text-red-300',
    'animate-pulse', 'animate-ping',
    'focus:ring-2', 'focus:ring-green-400', 'focus:ring-red-400',
    'focus:ring-yellow-300', 'focus:ring-orange-300', 'focus:ring-indigo-500',
    'focus:outline-none', 'disabled:opacity-50', 'disabled:cursor-not-allowed',
    // Size classes
    'w-4', 'h-4', 'w-8', 'h-8', 'w-12', 'h-12',
    'text-xs', 'text-sm', 'text-lg', 'text-xl', 'text-2xl', 'text-5xl',
    'p-2', 'p-3', 'p-4', 'p-6', 'px-3', 'px-4', 'py-2', 'py-3',
    'mt-2', 'mt-4', 'mt-6', 'mb-1', 'mb-2', 'mb-4', 'mr-1', 'mr-2', 'mr-3',
    'space-x-2', 'space-y-1', 'space-y-2', 'space-y-3',
    'rounded-md', 'rounded-lg', 'shadow-md', 'shadow-lg',
    'h-48', 'h-[600px]', 'max-w-xs',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
