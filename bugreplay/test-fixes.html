<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            margin-bottom: 20px;
        }
        
        .fix-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #2ecc71;
        }
        
        .fix-item.testing {
            border-left-color: #f39c12;
        }
        
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .test-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
        }
        
        .status.error {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
        }
        
        .status.warning {
            background: rgba(243, 156, 18, 0.2);
            border: 1px solid #f39c12;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 BugReplay Fixes Verification</h1>
        <p>This page helps verify that the connection and timestamp fixes are working correctly.</p>
        
        <div class="fix-item">
            <h3>✅ Fix 1: Content Script Connection</h3>
            <p><strong>Issue:</strong> "Could not establish connection. Receiving end does not exist."</p>
            <p><strong>Fix:</strong> Automatic content script injection with PING/PONG verification</p>
            <p><strong>Test:</strong> Start recording - should work without connection errors</p>
        </div>
        
        <div class="fix-item">
            <h3>✅ Fix 2: Timestamp Display</h3>
            <p><strong>Issue:</strong> "TypeError: t.timestamp.toLocaleTimeString is not a function"</p>
            <p><strong>Fix:</strong> Robust timestamp normalization in LogView component</p>
            <p><strong>Test:</strong> All log entries should display timestamps correctly</p>
        </div>
        
        <div class="fix-item">
            <h3>✅ Fix 3: Error Handling</h3>
            <p><strong>Issue:</strong> Poor error messages and silent failures</p>
            <p><strong>Fix:</strong> Enhanced error handling with helpful user feedback</p>
            <p><strong>Test:</strong> Clear error messages when issues occur</p>
        </div>
        
        <div class="fix-item">
            <h3>✅ Fix 4: Message Reliability</h3>
            <p><strong>Issue:</strong> Inconsistent message passing between components</p>
            <p><strong>Fix:</strong> Improved message handling with runtime error checking</p>
            <p><strong>Test:</strong> Reliable communication between popup and background</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🧪 Test Actions</h2>
        <p>Perform these actions while recording to test the fixes:</p>
        
        <div class="fix-item testing">
            <h4>Test Content Script Injection</h4>
            <button onclick="testContentScript()">Test Content Script</button>
            <button onclick="generateTimestampLogs()">Generate Timestamp Logs</button>
            <p>These should work without connection errors and display proper timestamps.</p>
        </div>
        
        <div class="fix-item testing">
            <h4>Test Error Handling</h4>
            <button onclick="triggerError()" class="test-button">Trigger JS Error</button>
            <button onclick="testNetworkError()" class="test-button">Test Network Error</button>
            <p>Errors should be captured and displayed with proper timestamps.</p>
        </div>
        
        <div class="fix-item testing">
            <h4>Test Message Reliability</h4>
            <button onclick="rapidActions()">Rapid Actions</button>
            <button onclick="stressTest()">Stress Test</button>
            <p>Multiple rapid actions should all be captured reliably.</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testStatus" class="status">
            Ready to test - start recording in the extension first
        </div>
        
        <div id="testLog" class="log">
            Test log will appear here...
        </div>
        
        <button onclick="clearTestLog()">Clear Log</button>
        <button onclick="runFullTest()">Run Full Test Suite</button>
    </div>
    
    <div class="test-container">
        <h2>📋 Verification Checklist</h2>
        <div class="fix-item">
            <h4>Before Testing:</h4>
            <ul>
                <li>✅ Extension loaded and built with latest fixes</li>
                <li>✅ Open BugReplay extension popup</li>
                <li>✅ Click "Start Recording"</li>
                <li>✅ Verify no connection errors in console</li>
            </ul>
        </div>
        
        <div class="fix-item">
            <h4>During Testing:</h4>
            <ul>
                <li>✅ All button clicks should be captured</li>
                <li>✅ Timestamps should display correctly in log view</li>
                <li>✅ No JavaScript errors in browser console</li>
                <li>✅ Error messages should be helpful and clear</li>
            </ul>
        </div>
        
        <div class="fix-item">
            <h4>After Testing:</h4>
            <ul>
                <li>✅ Stop recording successfully</li>
                <li>✅ Bug report should contain all test actions</li>
                <li>✅ All timestamps should be properly formatted</li>
                <li>✅ No missing or corrupted log entries</li>
            </ul>
        </div>
    </div>

    <script>
        let testCount = 0;
        
        function logTest(message, type = 'info') {
            testCount++;
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '✅';
            log.innerHTML += `[${timestamp}] ${typeIcon} Test ${testCount}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`BugReplay Fix Test: ${message}`);
            
            // Update status
            const status = document.getElementById('testStatus');
            status.textContent = `Last action: ${message}`;
            status.className = `status ${type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success'}`;
        }
        
        function testContentScript() {
            logTest('Testing content script injection and communication');
            console.log('Content script test - this should be captured');
            console.info('Info message with timestamp test');
        }
        
        function generateTimestampLogs() {
            logTest('Generating multiple log entries to test timestamp display');
            for (let i = 1; i <= 5; i++) {
                setTimeout(() => {
                    console.log(`Timestamp test log entry ${i} - ${new Date().toISOString()}`);
                }, i * 100);
            }
        }
        
        function triggerError() {
            logTest('Triggering JavaScript error to test error handling', 'warning');
            try {
                // This will throw a ReferenceError
                nonExistentFunction();
            } catch (error) {
                console.error('Test error generated:', error);
                logTest(`Error captured: ${error.message}`, 'error');
            }
        }
        
        async function testNetworkError() {
            logTest('Testing network error handling', 'warning');
            try {
                const response = await fetch('https://httpbin.org/status/500');
                logTest(`Network response: ${response.status}`, response.ok ? 'info' : 'error');
            } catch (error) {
                logTest(`Network error: ${error.message}`, 'error');
            }
        }
        
        function rapidActions() {
            logTest('Performing rapid actions to test message reliability');
            for (let i = 1; i <= 10; i++) {
                setTimeout(() => {
                    console.log(`Rapid action ${i}`);
                    if (i === 10) {
                        logTest('Rapid actions completed - all should be captured');
                    }
                }, i * 50);
            }
        }
        
        function stressTest() {
            logTest('Running stress test with mixed actions');
            
            // Generate various types of logs
            console.log('Stress test: console.log');
            console.warn('Stress test: console.warn');
            console.info('Stress test: console.info');
            
            // Trigger DOM changes
            const div = document.createElement('div');
            div.textContent = 'Stress test DOM element';
            document.body.appendChild(div);
            
            // Remove it after a moment
            setTimeout(() => {
                document.body.removeChild(div);
                logTest('Stress test completed');
            }, 1000);
        }
        
        function clearTestLog() {
            document.getElementById('testLog').innerHTML = '';
            testCount = 0;
            logTest('Test log cleared');
        }
        
        function runFullTest() {
            logTest('Starting full test suite...');
            
            setTimeout(() => testContentScript(), 500);
            setTimeout(() => generateTimestampLogs(), 1000);
            setTimeout(() => triggerError(), 2000);
            setTimeout(() => testNetworkError(), 3000);
            setTimeout(() => rapidActions(), 4000);
            setTimeout(() => stressTest(), 6000);
            setTimeout(() => logTest('Full test suite completed!'), 8000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logTest('BugReplay fixes test page loaded');
            console.log('BugReplay Fixes Test: Page ready for testing');
        });
        
        // Track all interactions
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                logTest(`Button clicked: ${e.target.textContent}`);
            }
        });
        
        // Test console override
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            // This tests that console overrides work properly
        };
    </script>
</body>
</html>
