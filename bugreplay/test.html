<!DOCTYPE html>
<html lang="en" style="height: 100%;">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BugReplay Extension Test</title>
    <link rel="stylesheet" href="./dist/assets/style-B-Jk9Nab.css">
    <style>
      /* Test container to simulate extension popup */
      .extension-popup {
        width: 600px;
        height: 600px;
        border: 2px solid #ccc;
        margin: 20px auto;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      }
    </style>
  </head>
  <body style="background: #f0f0f0; padding: 20px;">
    <h1 style="text-align: center; color: #333;">BugReplay Extension UI Test</h1>
    <div class="extension-popup">
      <div id="root"></div>
    </div>
    <script type="module" src="./dist/assets/popup-njN5m5xm.js"></script>
  </body>
</html>
