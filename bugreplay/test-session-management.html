<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Session Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            margin-bottom: 20px;
        }
        
        .test-step {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .test-step.warning {
            border-left-color: #FF9800;
        }
        
        .test-step.info {
            border-left-color: #2196F3;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .action-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .danger-button {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .step-number {
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .checklist li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🗄️ BugReplay Session Management Test</h1>
        <p>This page helps you test the new session management features of the BugReplay extension.</p>
        
        <div class="test-step info">
            <h3>📋 What's New in Session Management</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🗄️ Persistent Sessions</h4>
                    <p>All recording sessions are now saved permanently until manually deleted</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Session Browser</h4>
                    <p>View, search, and manage all your saved recording sessions</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 Bug Reports from History</h4>
                    <p>Generate bug reports from any previously recorded session</p>
                </div>
                <div class="feature-card">
                    <h4>💾 Data Management</h4>
                    <p>Monitor storage usage and export session data</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🧪 Testing Workflow</h2>
        
        <div class="test-step">
            <span class="step-number">1</span>
            <strong>Create Multiple Sessions</strong>
            <p>Record several short sessions to test the session management features:</p>
            <button onclick="createTestSession('Short Session')">Create Short Session</button>
            <button onclick="createTestSession('Medium Session')" class="action-button">Create Medium Session</button>
            <button onclick="createTestSession('Complex Session')" class="danger-button">Create Complex Session</button>
        </div>
        
        <div class="test-step">
            <span class="step-number">2</span>
            <strong>Test Session Recording</strong>
            <p>For each session, perform different types of actions:</p>
            <div class="feature-grid">
                <div>
                    <h4>Short Session Actions:</h4>
                    <button onclick="performAction('click', 'Short session click')">Click Action</button>
                    <button onclick="performAction('scroll', 'Short session scroll')">Scroll Action</button>
                </div>
                <div>
                    <h4>Medium Session Actions:</h4>
                    <button onclick="performAction('form', 'Medium session form')">Form Action</button>
                    <button onclick="performAction('network', 'Medium session network')">Network Action</button>
                </div>
                <div>
                    <h4>Complex Session Actions:</h4>
                    <button onclick="performAction('error', 'Complex session error')">Error Action</button>
                    <button onclick="performAction('dom', 'Complex session DOM')">DOM Action</button>
                </div>
            </div>
        </div>
        
        <div class="test-step">
            <span class="step-number">3</span>
            <strong>Access Sessions Tab</strong>
            <p>After recording sessions, test the session management interface:</p>
            <ul class="checklist">
                <li>Open BugReplay extension popup</li>
                <li>Click on "Sessions" tab in the header</li>
                <li>Verify all recorded sessions appear in the list</li>
                <li>Check session metadata (date, duration, size, etc.)</li>
            </ul>
        </div>
        
        <div class="test-step">
            <span class="step-number">4</span>
            <strong>Test Session Operations</strong>
            <p>Test various session management features:</p>
            <ul class="checklist">
                <li>Search for sessions by URL or title</li>
                <li>Sort sessions by date, duration, or size</li>
                <li>Select multiple sessions using checkboxes</li>
                <li>Generate bug report from a saved session</li>
                <li>Export a session as JSON file</li>
                <li>Delete individual sessions</li>
                <li>Perform bulk delete operations</li>
            </ul>
        </div>
        
        <div class="test-step warning">
            <span class="step-number">5</span>
            <strong>Test Storage Management</strong>
            <p>Verify storage usage and management features:</p>
            <ul class="checklist">
                <li>Check storage usage bar and statistics</li>
                <li>Verify session count and average size</li>
                <li>Test storage quota warnings (if applicable)</li>
                <li>Confirm deleted sessions free up storage space</li>
            </ul>
        </div>
        
        <div class="test-step">
            <span class="step-number">6</span>
            <strong>Test Persistence</strong>
            <p>Verify sessions persist across browser sessions:</p>
            <ul class="checklist">
                <li>Close and reopen the extension popup</li>
                <li>Restart the browser</li>
                <li>Verify all sessions are still available</li>
                <li>Test bug report generation from old sessions</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🎯 Interactive Test Actions</h2>
        <p>Use these actions to generate test data for your recording sessions:</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>User Interactions</h4>
                <button onclick="simulateClicks()">Multiple Clicks</button>
                <button onclick="simulateScrolling()">Scroll Test</button>
                <button onclick="simulateTyping()">Typing Test</button>
            </div>
            
            <div class="feature-card">
                <h4>Network Activity</h4>
                <button onclick="makeApiCall()">API Request</button>
                <button onclick="makeFailedRequest()">Failed Request</button>
                <button onclick="makeSlowRequest()">Slow Request</button>
            </div>
            
            <div class="feature-card">
                <h4>Console Activity</h4>
                <button onclick="generateLogs()">Console Logs</button>
                <button onclick="generateWarnings()">Warnings</button>
                <button onclick="generateErrors()">Errors</button>
            </div>
            
            <div class="feature-card">
                <h4>DOM Changes</h4>
                <button onclick="addElements()">Add Elements</button>
                <button onclick="removeElements()">Remove Elements</button>
                <button onclick="modifyElements()">Modify Elements</button>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 Test Activity Log</h2>
        <div id="testLog" class="log">
            Test activity will be logged here...
        </div>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="exportLog()" class="action-button">Export Log</button>
    </div>
    
    <div class="test-container">
        <h2>✅ Expected Results</h2>
        <div class="test-step">
            <h3>Session Management Features</h3>
            <ul class="checklist">
                <li>Sessions persist after recording completion</li>
                <li>All session data is preserved (video, screenshots, logs, HAR)</li>
                <li>Sessions tab shows comprehensive session list</li>
                <li>Search and filtering work correctly</li>
                <li>Bug reports can be generated from any saved session</li>
                <li>Session export produces valid JSON files</li>
                <li>Storage usage is accurately tracked and displayed</li>
                <li>Session deletion works for individual and bulk operations</li>
                <li>Sessions survive browser restarts and extension reloads</li>
            </ul>
        </div>
    </div>

    <script>
        let testCount = 0;
        let currentSessionType = null;
        
        function logTest(message, type = 'info') {
            testCount++;
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            log.innerHTML += `[${timestamp}] ${typeIcon} ${testCount}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`BugReplay Session Test: ${message}`);
        }
        
        function createTestSession(sessionType) {
            currentSessionType = sessionType;
            logTest(`Starting ${sessionType} - Begin recording in BugReplay extension now`, 'success');
            console.log(`Session Type: ${sessionType}`);
        }
        
        function performAction(actionType, description) {
            logTest(`Performing ${actionType} action: ${description}`);
            console.log(`Action: ${actionType} - ${description}`);
            
            // Trigger different types of actions based on type
            switch(actionType) {
                case 'click':
                    simulateClicks();
                    break;
                case 'scroll':
                    simulateScrolling();
                    break;
                case 'form':
                    simulateTyping();
                    break;
                case 'network':
                    makeApiCall();
                    break;
                case 'error':
                    generateErrors();
                    break;
                case 'dom':
                    addElements();
                    break;
            }
        }
        
        function simulateClicks() {
            logTest('Simulating multiple click events');
            for (let i = 1; i <= 3; i++) {
                setTimeout(() => {
                    console.log(`Simulated click ${i}`);
                    logTest(`Click event ${i} generated`);
                }, i * 500);
            }
        }
        
        function simulateScrolling() {
            logTest('Simulating scroll events');
            window.scrollTo({ top: window.scrollY + 200, behavior: 'smooth' });
            setTimeout(() => {
                window.scrollTo({ top: window.scrollY - 100, behavior: 'smooth' });
            }, 1000);
        }
        
        function simulateTyping() {
            logTest('Simulating typing in form fields');
            const input = document.createElement('input');
            input.type = 'text';
            input.placeholder = 'Test input field';
            input.style.margin = '10px';
            input.style.padding = '8px';
            input.style.borderRadius = '4px';
            input.style.border = 'none';
            document.body.appendChild(input);
            input.focus();
            
            // Simulate typing
            const text = 'Test session data';
            let i = 0;
            const typeInterval = setInterval(() => {
                if (i < text.length) {
                    input.value += text[i];
                    i++;
                } else {
                    clearInterval(typeInterval);
                    logTest('Typing simulation completed');
                }
            }, 100);
        }
        
        async function makeApiCall() {
            logTest('Making API request');
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
                const data = await response.json();
                logTest(`API request successful: ${response.status}`);
                console.log('API Response:', data);
            } catch (error) {
                logTest(`API request failed: ${error.message}`, 'error');
            }
        }
        
        async function makeFailedRequest() {
            logTest('Making failed API request');
            try {
                const response = await fetch('https://httpbin.org/status/404');
                logTest(`Failed request completed: ${response.status}`, 'warning');
            } catch (error) {
                logTest(`Failed request error: ${error.message}`, 'error');
            }
        }
        
        async function makeSlowRequest() {
            logTest('Making slow API request');
            try {
                const response = await fetch('https://httpbin.org/delay/2');
                logTest(`Slow request completed: ${response.status}`);
            } catch (error) {
                logTest(`Slow request error: ${error.message}`, 'error');
            }
        }
        
        function generateLogs() {
            logTest('Generating console logs');
            console.log('Test console log message');
            console.info('Test console info message');
            console.debug('Test console debug message');
        }
        
        function generateWarnings() {
            logTest('Generating console warnings');
            console.warn('Test console warning message');
            console.warn('Another warning with object:', { test: true, session: currentSessionType });
        }
        
        function generateErrors() {
            logTest('Generating console errors');
            console.error('Test console error message');
            try {
                throw new Error('Test error for session recording');
            } catch (error) {
                console.error('Caught error:', error);
                logTest('Error generated and logged', 'error');
            }
        }
        
        function addElements() {
            logTest('Adding DOM elements');
            const container = document.createElement('div');
            container.style.cssText = 'background: rgba(255,255,255,0.1); padding: 10px; margin: 10px; border-radius: 5px;';
            container.innerHTML = `<p>Dynamic element added at ${new Date().toLocaleTimeString()}</p>`;
            document.body.appendChild(container);
        }
        
        function removeElements() {
            logTest('Removing DOM elements');
            const elements = document.querySelectorAll('div[style*="rgba(255,255,255,0.1)"]');
            if (elements.length > 0) {
                elements[0].remove();
                logTest('DOM element removed');
            } else {
                logTest('No elements to remove', 'warning');
            }
        }
        
        function modifyElements() {
            logTest('Modifying DOM elements');
            const elements = document.querySelectorAll('div[style*="rgba(255,255,255,0.1)"]');
            if (elements.length > 0) {
                elements[0].style.background = 'rgba(255,0,0,0.2)';
                elements[0].innerHTML += '<br><small>Modified at ' + new Date().toLocaleTimeString() + '</small>';
                logTest('DOM element modified');
            } else {
                addElements();
            }
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            testCount = 0;
            logTest('Test log cleared');
        }
        
        function exportLog() {
            const logContent = document.getElementById('testLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bugreplay-session-test-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            logTest('Test log exported');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logTest('BugReplay Session Management test page loaded');
            console.log('BugReplay Session Management Test: Ready for testing');
        });
        
        // Track page interactions for testing
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                logTest(`Button clicked: ${e.target.textContent}`);
            }
        });
    </script>
</body>
</html>
