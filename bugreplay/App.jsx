
/* global chrome */

import React, { useState, useEffect, useCallback } from 'react';
import { Header } from './components/Header.jsx';
import { Controls } from './components/Controls.jsx';
import { LogView } from './components/LogView.jsx';
import { BugReportModal } from './components/BugReportModal.jsx';
import { Sessions } from './components/Sessions.jsx';
import { LogType } from './types.js';

const MAX_LOGS = 100;

/**
 * Main App component for the BugReplay Chrome extension
 * @returns {JSX.Element} The main app component
 */
const App = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [logs, setLogs] = useState([]);
  const [simulatedError, setSimulatedError] = useState(null);
  const [showBugReportModal, setShowBugReportModal] = useState(false);
  const [bugReport, setBugReport] = useState(null);
  const [activeTabInfo, setActiveTabInfo] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentView, setCurrentView] = useState('recording'); // 'recording' or 'sessions'
  const [capturingScreenshot, setCapturingScreenshot] = useState(false);
  const [videoRecording, setVideoRecording] = useState(false);
  const [storageUsage, setStorageUsage] = useState(null);

  /**
   * Add a new log entry to the logs array
   * @param {Object} newLogEntryData - The log entry data
   * @param {string} newLogEntryData.type - Type of log entry
   * @param {string} newLogEntryData.message - Log message
   * @param {string} [newLogEntryData.status] - Optional status
   * @param {string|Date} [newLogEntryData.timestamp] - Optional timestamp
   */
  const addLog = useCallback((newLogEntryData) => {
    setLogs(prevLogs => {
      const entry = {
        id: Date.now() + Math.random(),
        type: newLogEntryData.type,
        message: newLogEntryData.message,
        status: newLogEntryData.status,
        timestamp: typeof newLogEntryData.timestamp === 'string' ? new Date(newLogEntryData.timestamp) : newLogEntryData.timestamp || new Date(),
      };
      const updatedLogs = [entry, ...prevLogs];
      return updatedLogs.length > MAX_LOGS ? updatedLogs.slice(0, MAX_LOGS) : updatedLogs;
    });
  }, []);

  /**
   * Restore recording state from background script
   */
  const restoreRecordingState = useCallback(async () => {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      try {
        // Get current recording state from background script
        chrome.runtime.sendMessage({ type: 'GET_RECORDING_STATE' }, (response) => {
          if (response && response.state) {
            const isCurrentlyRecording = response.state === 'RECORDING';
            setIsRecording(isCurrentlyRecording);

            if (isCurrentlyRecording && response.session) {
              addLog({
                type: LogType.SYSTEM,
                message: `Restored recording session: ${response.session.id}`
              });

              // Get session logs from background script
              chrome.runtime.sendMessage({ type: 'GET_SESSION_LOGS' }, (logsResponse) => {
                if (logsResponse && logsResponse.logs) {
                  // Restore logs from the session
                  setLogs(logsResponse.logs.slice(-MAX_LOGS));
                  addLog({
                    type: LogType.SYSTEM,
                    message: `Restored ${logsResponse.logs.length} log entries from session`
                  });
                }
              });
            } else {
              addLog({ type: LogType.SYSTEM, message: 'No active recording session found' });
            }
          }
          setIsInitialized(true);
        });
      } catch (error) {
        console.error('BugReplay: Error restoring state:', error);
        addLog({ type: LogType.SYSTEM, message: `Error restoring state: ${error.message}`, status: 'error' });
        setIsInitialized(true);
      }
    } else {
      setIsInitialized(true);
    }
  }, [addLog]);

  useEffect(() => {
    let messageListener = null;

    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      messageListener = (request, sender, sendResponse) => {
        if (request.type === 'LOG_ENTRY_FROM_CONTENT' && request.payload) {
          addLog(request.payload);
        }
      };
      chrome.runtime.onMessage.addListener(messageListener);
    } else {
      console.warn('BugReplay: chrome.runtime.onMessage is not available. Ensure this is running in an extension popup.');
      addLog({type: LogType.SYSTEM, message: 'Error: Extension APIs for messaging are not available.', status: 'error'});
    }

    // Restore recording state when popup opens
    restoreRecordingState();

    if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.query) {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime && chrome.runtime.lastError) {
          console.warn('BugReplay: Error querying tabs in useEffect:', chrome.runtime.lastError.message);
          addLog({ type: LogType.SYSTEM, message: `Error querying initial tab: ${chrome.runtime.lastError.message}` });
          setActiveTabInfo(null);
          return;
        }
        
        if (tabs && tabs[0] && (tabs[0].url || tabs[0].title)) {
          setActiveTabInfo({ url: tabs[0].url || 'N/A', title: tabs[0].title || tabs[0].url || 'Untitled Tab' });
        } else {
          console.warn('BugReplay: No active tab found during initial query in useEffect (or tab has no URL/title).');
          addLog({ type: LogType.SYSTEM, message: 'No active tab found for initial info, or tab details incomplete.' });
          setActiveTabInfo(null);
        }
      });
    } else {
      console.warn('BugReplay: chrome.tabs.query is not available in useEffect.');
      addLog({ type: LogType.SYSTEM, message: 'Tab query API not available on init.' });
      setActiveTabInfo(null);
    }

    return () => {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage && messageListener) {
        chrome.runtime.onMessage.removeListener(messageListener);
      }
    };
  }, [addLog, restoreRecordingState]);

  /**
   * Fetch storage usage information
   */
  const fetchStorageUsage = () => {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: 'GET_STORAGE_USAGE' }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn('BugReplay: Could not get storage usage:', chrome.runtime.lastError.message);
          return;
        }

        if (response && response.success) {
          setStorageUsage(response.usage);

          // Show warning if storage is getting full
          if (response.usage.percentage > 80) {
            addLog({
              type: LogType.SYSTEM,
              message: `Storage usage is at ${response.usage.percentage}%. Consider deleting old sessions to free space.`,
              status: 'warning'
            });
          }
        }
      });
    }
  };

  /**
   * Send a message to the active content script
   * @param {Object} message - The message to send
   * @param {Function} [callback] - Optional callback for response
   */
  const sendMessageToActiveContentScript = (message, callback) => {
    const handleResponse = (responseFromCallback) => {
      if (chrome.runtime && chrome.runtime.lastError) {
        // Prefer explicit error in response if available, otherwise use lastError
        const errorMessage = responseFromCallback?.error || chrome.runtime.lastError.message;
        console.error(`BugReplay: Error during sendMessage operation for ${message.type}:`, errorMessage);
        if (callback) callback({ error: errorMessage, details: responseFromCallback });
        return;
      }
      if (callback) callback(responseFromCallback);
    };
  
    if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.query && chrome.tabs.sendMessage) {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime && chrome.runtime.lastError) {
          console.error('BugReplay: Error querying active tab for direct send:', chrome.runtime.lastError.message);
          // Fallback if query fails
          if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
            console.warn("BugReplay: Query active tab failed. Attempting to send message via background script.", message);
            addLog({type: LogType.SYSTEM, message: "Query active tab failed, trying background relay."});
            chrome.runtime.sendMessage({ ...message, target: 'content_script_via_background' }, handleResponse);
          } else {
            if (callback) callback({ error: `Error querying active tab: ${chrome.runtime.lastError.message} and background relay unavailable.` });
          }
          return;
        }

        if (tabs && tabs[0] && tabs[0].id) {
          chrome.tabs.sendMessage(tabs[0].id, message, handleResponse);
        } else {
          console.error("BugReplay: No active tab found to send message directly.", message);
          if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
            console.warn("BugReplay: No active tab by direct query. Attempting to send message via background script.", message);
            addLog({type: LogType.SYSTEM, message: "No active tab direct, trying background relay."});
            chrome.runtime.sendMessage({ ...message, target: 'content_script_via_background' }, handleResponse);
          } else {
            if (callback) callback({ error: "No active tab found and background script messaging unavailable." });
          }
        }
      });
    } else if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      console.warn("BugReplay: chrome.tabs API not available. Attempting to send message via background script.", message);
      addLog({type: LogType.SYSTEM, message: "Tab API missing, trying background relay."});
      chrome.runtime.sendMessage({ ...message, target: 'content_script_via_background' }, handleResponse);
    } else {
      console.error("BugReplay: Critical - chrome.tabs and chrome.runtime.sendMessage are not available.", message);
      addLog({type: LogType.SYSTEM, message: "FATAL: All messaging APIs unavailable."});
      if (callback) callback({error: "Extension API for sending messages (direct or via background) not available."});
    }
  };

  const startRecording = () => {
    console.log('BugReplay: startRecording invoked. Starting comprehensive recording...');
    setIsRecording(true);
    setLogs([]);
    setSimulatedError(null);
    setShowBugReportModal(false);
    addLog({ type: LogType.SYSTEM, message: 'Starting comprehensive recording...' });

    // Send message to background script to start recording
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: 'START_RECORDING_BACKGROUND' }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('BugReplay: Runtime error starting recording:', chrome.runtime.lastError.message);
          addLog({ type: LogType.SYSTEM, message: `Runtime error: ${chrome.runtime.lastError.message}`, status: 'error' });
          setIsRecording(false);
          return;
        }

        if (response && response.success) {
          console.log('BugReplay: Recording started successfully:', response);
          addLog({ type: LogType.SYSTEM, message: `Recording started with session ID: ${response.sessionId}` });
          addLog({ type: LogType.SYSTEM, message: 'Background recording active. Content script will be injected automatically.' });
        } else {
          console.error('BugReplay: Error starting recording:', response?.error || 'Unknown error');
          const errorMsg = response?.error || 'Unknown error';
          addLog({ type: LogType.SYSTEM, message: `Error starting recording: ${errorMsg}`, status: 'error' });

          // Provide helpful error messages
          if (errorMsg.includes('content script')) {
            addLog({ type: LogType.SYSTEM, message: 'Note: Some page interactions may not be captured, but network and console logs will still be recorded.', status: 'warning' });
          }

          setIsRecording(false);
        }
      });
    } else {
      console.error('BugReplay: Chrome runtime not available');
      addLog({ type: LogType.SYSTEM, message: 'Error: Chrome runtime not available', status: 'error' });
      setIsRecording(false);
    }
  };

  const stopRecording = useCallback(() => {
    setIsRecording(false);
    addLog({ type: LogType.SYSTEM, message: 'Stopping recording and processing data...' });

    // Send message to background script to stop recording
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: 'STOP_RECORDING_BACKGROUND' }, (response) => {
        if (response && response.success) {
          console.log('BugReplay: Recording stopped successfully:', response);
          addLog({ type: LogType.SYSTEM, message: 'Recording stopped. Processing session data...' });

          // Generate bug report from recording session
          if (response.session) {
            generateBugReportFromSession(response.session);
          }
        } else {
          console.error('BugReplay: Error stopping recording:', response?.error || 'Unknown error');
          addLog({ type: LogType.SYSTEM, message: `Error stopping recording: ${response?.error || 'Unknown error'}` });
        }
      });
    } else {
      console.error('BugReplay: Chrome runtime not available');
      addLog({ type: LogType.SYSTEM, message: 'Error: Chrome runtime not available' });
    }
  }, [addLog]);

  /**
   * Generate a bug report from a recording session
   * @param {Object} session - Recording session data
   */
  const generateBugReportFromSession = (session) => {
    try {
      if (!session) {
        console.error('BugReplay: No session data provided for bug report generation');
        addLog({ type: LogType.SYSTEM, message: 'Error: No session data available for bug report', status: 'error' });
        return;
      }

      const pageUrl = session.url || 'Current Page';
      const pageTitle = session.title || 'Unknown Title';

      // Extract user actions for steps to reproduce with error handling
      let userActions = '';
      try {
        userActions = (session.logs || [])
          .filter(log => log && log.type === LogType.USER_ACTION)
          .map((log, index) => `${index + 1}. ${log.message || 'Unknown action'}`)
          .join('\n') || `1. Navigate to ${pageUrl}\n2. Perform relevant actions...`;
      } catch (error) {
        console.error('BugReplay: Error extracting user actions:', error);
        userActions = `1. Navigate to ${pageUrl}\n2. Perform relevant actions...`;
      }

      // Extract errors from logs with error handling
      let errors = '';
      try {
        errors = (session.logs || [])
          .filter(log => log && (log.type === LogType.CONSOLE_ERROR || (log.type === LogType.NETWORK_RESPONSE && log.status === 'error')))
          .map(log => log.message || 'Unknown error')
          .join('\n') || 'No specific errors detected during recording';
      } catch (error) {
        console.error('BugReplay: Error extracting errors:', error);
        errors = 'No specific errors detected during recording';
      }

      // Generate logs string with error handling
      let logsString = '';
      try {
        logsString = (session.logs || [])
          .map(log => {
            const timestamp = log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : 'Unknown time';
            const type = log.type || 'UNKNOWN';
            const message = log.message || 'No message';
            const status = log.status ? ` (${log.status})` : '';
            return `${timestamp} [${type}] ${message}${status}`;
          })
          .join('\n');
      } catch (error) {
        console.error('BugReplay: Error generating logs string:', error);
        logsString = 'Error processing logs';
      }

      // Handle screenshots with error handling
      let screenshotUrl = null;
      let attachments = [];
      try {
        if (session.screenshots && session.screenshots.length > 0) {
          const lastScreenshot = session.screenshots[session.screenshots.length - 1];
          screenshotUrl = typeof lastScreenshot === 'string' ? lastScreenshot : lastScreenshot?.dataUrl;
        }

        attachments = [
          ...(session.screenshots || []).map(screenshot =>
            typeof screenshot === 'string' ? screenshot : screenshot?.dataUrl
          ).filter(Boolean),
          ...(session.videoUrl ? [session.videoUrl] : []),
          ...(session.harData ? ['HAR File Available'] : [])
        ];
      } catch (error) {
        console.error('BugReplay: Error processing screenshots:', error);
        screenshotUrl = null;
        attachments = [];
      }

      const generatedBugReport = {
        title: `[Bug Report] Issue recorded on ${pageTitle.substring(0,50)}${pageTitle.length > 50 ? '...' : ''}`,
        stepsToReproduce: userActions,
        expectedResult: 'The workflow should complete successfully without errors.',
        actualResult: errors,
        environment: `Browser: ${typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown'}\nPage URL: ${pageUrl}\nPage Title: ${pageTitle}\nRecording Duration: ${session.endTime ? Math.round((new Date(session.endTime) - new Date(session.startTime)) / 1000) : 'Unknown'} seconds`,
        logs: logsString,
        screenshotUrl: screenshotUrl,
        recordingSession: session,
        attachments: attachments
      };

      setBugReport(generatedBugReport);
      setShowBugReportModal(true);
      addLog({ type: LogType.SYSTEM, message: 'Bug report generated successfully' });

    } catch (error) {
      console.error('BugReplay: Error generating bug report:', error);
      addLog({ type: LogType.SYSTEM, message: `Error generating bug report: ${error.message}`, status: 'error' });
    }
  };

  /**
   * Generate a bug report based on error information (legacy for simulated errors)
   * @param {'js'|'network'} errorType - Type of error
   * @param {string} errorMessage - Error message
   * @param {string} screenshotUrl - Screenshot URL
   */
  const generateBugReport = (errorType, errorMessage, screenshotUrl) => {
    const pageUrl = activeTabInfo?.url || 'Current Page';
    const pageTitle = activeTabInfo?.title || 'Unknown Title';
    const generatedBugReport = {
      title: `[Bug] ${errorType === 'js' ? 'JavaScript error' : 'Network failure'} on ${pageTitle.substring(0,50)}${pageTitle.length > 50 ? '...' : ''}`,
      stepsToReproduce: logs
        .filter(log => log.type === LogType.USER_ACTION)
        .slice(-5)
        .map((log, index) => `${index + 1}. ${log.message.replace('User clicked on', 'Clicked')}`)
        .join('\n') || `1. Navigate to ${pageUrl}\n2. Perform relevant actions...`,
      expectedResult: 'The action should complete successfully.',
      actualResult: `${errorType === 'js' ? 'JavaScript Error' : 'Network Error'}: ${errorMessage}`,
      environment: `Browser: ${typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown'}\nPage URL: ${pageUrl}\nPage Title: ${pageTitle}`,
      logs: logs.map(log => `${log.timestamp.toLocaleTimeString()} [${log.type}] ${log.message}${log.status ? ` (${log.status})` : ''}`).join('\n'),
      screenshotUrl: screenshotUrl,
    };
    setBugReport(generatedBugReport);
    setShowBugReportModal(true);
  };


  /**
   * Simulate an error for testing purposes
   * @param {'js'|'network'} type - Type of error to simulate
   */
  const simulateError = (type) => {
    // It's better UX to not auto-stop recording here, let user decide.
    // If recording, the error will be part of the recording.
    // If user wants to report *this specific error*, they can stop recording then report.
    // However, the current flow ties simulation to immediate report generation.
    // For now, keep stopRecording if that's the intended UX for *simulated* errors.
    if (isRecording) {
        stopRecording(); 
    }
    const errorMessage = type === 'js' 
      ? 'TypeError: Cannot read properties of undefined (reading "value")' 
      : 'Failed to fetch API: POST /api/submit - 500 Internal Server Error';
    
    const errorLogType = type === 'js' ? LogType.CONSOLE_ERROR : LogType.NETWORK_REQUEST;
    const errorStatus = type === 'js' ? undefined : 'error';

    addLog({ type: errorLogType, message: errorMessage, status: errorStatus });
    addLog({ type: LogType.SYSTEM, message: `Screenshot capture initiated for simulated error.` });
    
    if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.captureVisibleTab) {
      chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
        if (chrome.runtime && chrome.runtime.lastError || !dataUrl) {
          const captureError = chrome.runtime?.lastError?.message || 'No data URL returned';
          console.error('BugReplay: Failed to capture screenshot:', captureError);
          addLog({ type: LogType.SYSTEM, message: `Failed to capture screenshot: ${captureError}` });
          generateBugReport(type, errorMessage, 'placeholder_screenshot.png');
          return;
        }
        addLog({ type: LogType.SYSTEM, message: `Screenshot captured successfully.` });
        
        const newSimulatedError = {
          type: type === 'js' ? 'JavaScript Error' : 'Network Error',
          message: errorMessage,
          screenshotUrl: dataUrl,
        };
        setSimulatedError(newSimulatedError);
        generateBugReport(type, errorMessage, dataUrl);
      });
    } else {
      console.error('BugReplay: chrome.tabs.captureVisibleTab is not available.');
      addLog({ type: LogType.SYSTEM, message: 'Error: Screenshot API not available.' });
      generateBugReport(type, errorMessage, 'placeholder_screenshot.png'); 
    }
  };

  /**
   * Capture a manual screenshot during recording
   */
  const captureManualScreenshot = () => {
    if (!isRecording) {
      addLog({ type: LogType.SYSTEM, message: 'Cannot capture screenshot: No active recording session', status: 'error' });
      return;
    }

    setCapturingScreenshot(true);

    // Send message to background script to capture manual screenshot
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: 'CAPTURE_MANUAL_SCREENSHOT' }, (response) => {
        setCapturingScreenshot(false);

        if (chrome.runtime.lastError) {
          console.error('BugReplay: Runtime error capturing manual screenshot:', chrome.runtime.lastError.message);
          addLog({
            type: LogType.SYSTEM,
            message: `Failed to capture manual screenshot: ${chrome.runtime.lastError.message}`,
            status: 'error'
          });
          return;
        }

        if (response && response.success) {
          addLog({
            type: LogType.SYSTEM,
            message: 'Manual screenshot captured and saved successfully'
          });
        } else {
          addLog({
            type: LogType.SYSTEM,
            message: `Failed to capture manual screenshot: ${response?.error || 'Unknown error'}`,
            status: 'error'
          });
        }
      });
    } else {
      setCapturingScreenshot(false);
      addLog({
        type: LogType.SYSTEM,
        message: 'Cannot capture screenshot: Extension API not available',
        status: 'error'
      });
    }
  };

  /**
   * Start video recording manually
   */
  const startVideoRecording = () => {
    if (!isRecording) {
      addLog({ type: LogType.SYSTEM, message: 'Cannot start video recording: No active recording session', status: 'error' });
      return;
    }

    if (videoRecording) {
      addLog({ type: LogType.SYSTEM, message: 'Video recording is already active', status: 'warning' });
      return;
    }

    addLog({ type: LogType.SYSTEM, message: 'Starting video recording...' });

    // Send message to content script to start video recording
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: 'START_VIDEO_RECORDING_MANUAL' }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('BugReplay: Runtime error starting video recording:', chrome.runtime.lastError.message);
          addLog({
            type: LogType.SYSTEM,
            message: `Failed to start video recording: ${chrome.runtime.lastError.message}`,
            status: 'error'
          });
          return;
        }

        if (response && response.success) {
          setVideoRecording(true);
          addLog({
            type: LogType.SYSTEM,
            message: 'Video recording started successfully - screen capture active'
          });
        } else {
          const errorMessage = response?.error || 'Unknown error';
          const guidance = response?.guidance || '';

          let fullMessage = `Failed to start video recording: ${errorMessage}`;
          if (guidance) {
            fullMessage += ` ${guidance}`;
          }

          addLog({
            type: LogType.SYSTEM,
            message: fullMessage,
            status: 'error'
          });
        }
      });
    } else {
      addLog({
        type: LogType.SYSTEM,
        message: 'Cannot start video recording: Extension API not available',
        status: 'error'
      });
    }
  };

  /**
   * Handle creating a GitHub issue from the bug report
   * @param {Object} updatedReport - The updated bug report
   */
  const handleCreateIssue = (updatedReport) => {
    console.log('Simulating GitHub issue creation:', updatedReport);
    addLog({ type: LogType.SYSTEM, message: `Bug report "${updatedReport.title}" submitted (simulated).` });
    setShowBugReportModal(false);
    setSimulatedError(null); 
  };

  const handleCloseModal = () => {
    setShowBugReportModal(false);
    setSimulatedError(null);
  };

  /**
   * Handle navigation between views
   */
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  /**
   * Handle bug report generation from saved session
   */
  const handleGenerateBugReportFromSession = (session) => {
    generateBugReportFromSession(session);
    setCurrentView('recording'); // Switch back to recording view to show the modal
  };
  

  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <div className="flex flex-col h-full w-full bg-gradient-to-br from-slate-900 via-slate-800 to-gray-900 text-slate-100">
        <Header />
        <main className="flex-1 p-4 overflow-y-auto flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-slate-300">Restoring recording state...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full w-full bg-gradient-to-br from-slate-900 via-slate-800 to-gray-900 text-slate-100">
      <Header
        currentView={currentView}
        onViewChange={handleViewChange}
      />

      <main className="flex-1 p-4 overflow-y-auto">
        {currentView === 'recording' ? (
          <>
            <Controls
              isRecording={isRecording}
              onStartRecording={startRecording}
              onStopRecording={stopRecording}
              onSimulateJSError={() => simulateError('js')}
              onSimulateNetworkError={() => simulateError('network')}
              onCaptureScreenshot={captureManualScreenshot}
              capturingScreenshot={capturingScreenshot}
              onStartVideoRecording={startVideoRecording}
              videoRecording={videoRecording}
            />

            {simulatedError && !showBugReportModal && (
              <div className="mt-6 p-4 bg-red-700/30 border border-red-500 rounded-md text-center">
                <h3 className="text-lg font-semibold text-red-300">Error Detected!</h3>
                <p className="text-red-400">{simulatedError.message}</p>
                {simulatedError.screenshotUrl && <img src={simulatedError.screenshotUrl} alt="Error preview" className="max-w-xs mx-auto my-2 rounded border border-red-600"/> }
                <button
                  onClick={() => setShowBugReportModal(true)}
                  className="mt-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-500 text-white rounded-md transition-colors"
                >
                  View Bug Report
                </button>
              </div>
            )}

            <LogView logs={logs} />
          </>
        ) : (
          <Sessions
            onGenerateBugReport={handleGenerateBugReportFromSession}
            onClose={() => setCurrentView('recording')}
          />
        )}
      </main>

      {showBugReportModal && bugReport && (
        <BugReportModal
          report={bugReport}
          onClose={handleCloseModal}
          onSubmit={handleCreateIssue}
        />
      )}

      {currentView === 'recording' && (
        <footer className="mt-4 p-2 text-center text-xs text-slate-500 border-t border-slate-700">
          BugReplay &copy; {new Date().getFullYear()}. Extension Mode.
        </footer>
      )}
    </div>
  );
};

export default App;
