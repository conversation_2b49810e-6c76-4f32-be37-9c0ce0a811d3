<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Video Recording & HAR Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h2 {
            color: #4a5568;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .video-btn {
            background: #e53e3e;
            font-weight: bold;
            font-size: 16px;
        }
        .video-btn:hover {
            background: #c53030;
        }
        .network-btn {
            background: #38a169;
        }
        .network-btn:hover {
            background: #2f855a;
        }
        .test-instructions {
            background: #bee3f8;
            border: 2px solid #3182ce;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .test-instructions h1 {
            color: #2c5282;
            margin-top: 0;
        }
        .feature-highlight {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            padding: 15px;
            margin: 15px 0;
        }
        .log-display {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-recording {
            background: #e53e3e;
            animation: pulse 1s infinite;
        }
        .status-idle {
            background: #a0aec0;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .network-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #edf2f7;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
        }
        .stat-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="test-instructions">
        <h1>🎥 BugReplay Video Recording & HAR Testing</h1>
        <p><strong>Test the following critical fixes:</strong></p>
        <ol>
            <li><strong>Working Video Recording:</strong> Use "Start Video Recording" button to capture actual screen video</li>
            <li><strong>Accurate HAR Export:</strong> Generate network requests and verify comprehensive HAR data</li>
        </ol>
        <div class="feature-highlight">
            <strong>🔧 Critical Fixes:</strong> Real video capture with getDisplayMedia() API and comprehensive HAR data matching Chrome DevTools quality!
        </div>
    </div>

    <div class="container">
        <!-- Video Recording Test Section -->
        <div class="section">
            <h2>🎥 Video Recording Test</h2>
            <p>Test the working video recording functionality:</p>
            
            <div style="margin: 20px 0;">
                <span class="status-indicator status-idle" id="video-status"></span>
                <span id="video-status-text">Video Recording: Idle</span>
            </div>
            
            <button class="video-btn" id="start-video-btn" onclick="startVideoRecording()">
                🎥 Start Video Recording
            </button>
            <button id="stop-video-btn" onclick="stopVideoRecording()" disabled>
                ⏹️ Stop Video Recording
            </button>
            
            <div class="feature-highlight">
                <strong>Instructions:</strong>
                <ol>
                    <li>Start BugReplay recording first</li>
                    <li>Click "Start Video Recording" button</li>
                    <li>Grant screen capture permission when prompted</li>
                    <li>Perform actions on this page</li>
                    <li>Stop video recording</li>
                    <li>Check Downloads folder for video file</li>
                </ol>
            </div>
        </div>

        <!-- Network Activity Test Section -->
        <div class="section">
            <h2>🌐 Network Activity & HAR Test</h2>
            <p>Generate various network requests to test comprehensive HAR data capture:</p>
            
            <div class="network-stats" id="network-stats">
                <div class="stat-card">
                    <div class="stat-number" id="requests-count">0</div>
                    <div class="stat-label">Requests Made</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="success-count">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="error-count">0</div>
                    <div class="stat-label">Errors</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="data-transferred">0 KB</div>
                    <div class="stat-label">Data Transferred</div>
                </div>
            </div>
            
            <div style="margin: 20px 0;">
                <button class="network-btn" onclick="makeGetRequest()">📥 GET Request</button>
                <button class="network-btn" onclick="makePostRequest()">📤 POST Request</button>
                <button class="network-btn" onclick="makePutRequest()">🔄 PUT Request</button>
                <button class="network-btn" onclick="makeDeleteRequest()">🗑️ DELETE Request</button>
                <button class="network-btn" onclick="loadImage()">🖼️ Load Image</button>
                <button class="network-btn" onclick="loadCSS()">🎨 Load CSS</button>
                <button class="network-btn" onclick="loadScript()">📜 Load Script</button>
                <button class="network-btn" onclick="makeErrorRequest()">❌ Error Request</button>
            </div>
            
            <div class="feature-highlight">
                <strong>HAR Testing Instructions:</strong>
                <ol>
                    <li>Start BugReplay recording</li>
                    <li>Click various network request buttons above</li>
                    <li>Stop recording and export HAR file</li>
                    <li>Compare with Chrome DevTools Network tab export</li>
                    <li>Verify: headers, timing, request/response data, errors</li>
                </ol>
            </div>
        </div>

        <!-- Interactive Elements for Video Testing -->
        <div class="section">
            <h2>🎮 Interactive Elements for Video Testing</h2>
            <p>Use these elements to create visual activity for video recording:</p>
            
            <div style="margin: 20px 0;">
                <button onclick="animateElement()">🎭 Animate Element</button>
                <button onclick="changeColors()">🌈 Change Colors</button>
                <button onclick="showModal()">🪟 Show Modal</button>
                <button onclick="scrollToBottom()">⬇️ Scroll Down</button>
                <button onclick="addContent()">➕ Add Content</button>
            </div>
            
            <div id="animation-target" style="width: 100px; height: 100px; background: #667eea; margin: 20px 0; border-radius: 8px; transition: all 0.5s ease;"></div>
            
            <div id="dynamic-content"></div>
        </div>

        <!-- Activity Log -->
        <div class="section">
            <h2>📊 Activity Log</h2>
            <p>Monitor network requests and video recording activity:</p>
            <div class="log-display" id="activity-log">
                <div>🎯 BugReplay Video & HAR testing initialized...</div>
                <div>📝 Perform actions above to generate activity</div>
            </div>
        </div>
    </div>

    <!-- Hidden Modal for Testing -->
    <div id="test-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="background: white; margin: 15% auto; padding: 20px; border-radius: 8px; width: 80%; max-width: 500px;">
            <h2>Test Modal</h2>
            <p>This modal is for testing video recording of UI interactions.</p>
            <button onclick="hideModal()">Close Modal</button>
        </div>
    </div>

    <script>
        let requestCount = 0;
        let successCount = 0;
        let errorCount = 0;
        let dataTransferred = 0;
        let videoRecording = false;

        function addToLog(message) {
            const logDisplay = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #68d391;">[${timestamp}]</span> ${message}`;
            logDisplay.appendChild(logEntry);
            logDisplay.scrollTop = logDisplay.scrollHeight;
            
            // Keep only last 20 entries
            while (logDisplay.children.length > 20) {
                logDisplay.removeChild(logDisplay.firstChild);
            }
        }

        function updateNetworkStats() {
            document.getElementById('requests-count').textContent = requestCount;
            document.getElementById('success-count').textContent = successCount;
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('data-transferred').textContent = Math.round(dataTransferred / 1024) + ' KB';
        }

        function startVideoRecording() {
            if (videoRecording) return;
            
            videoRecording = true;
            document.getElementById('video-status').className = 'status-indicator status-recording';
            document.getElementById('video-status-text').textContent = 'Video Recording: Active';
            document.getElementById('start-video-btn').disabled = true;
            document.getElementById('stop-video-btn').disabled = false;
            
            addToLog('🎥 Video recording started - screen capture active');
        }

        function stopVideoRecording() {
            if (!videoRecording) return;
            
            videoRecording = false;
            document.getElementById('video-status').className = 'status-indicator status-idle';
            document.getElementById('video-status-text').textContent = 'Video Recording: Stopped';
            document.getElementById('start-video-btn').disabled = false;
            document.getElementById('stop-video-btn').disabled = true;
            
            addToLog('⏹️ Video recording stopped - check Downloads folder');
        }

        async function makeRequest(method, url, data = null) {
            requestCount++;
            addToLog(`📡 Making ${method} request to ${url}`);
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Test-Header': 'BugReplay-Test',
                        'User-Agent': 'BugReplay-Extension-Test'
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const responseData = await response.text();
                
                dataTransferred += responseData.length;
                
                if (response.ok) {
                    successCount++;
                    addToLog(`✅ ${method} ${response.status} ${url} (${responseData.length} bytes)`);
                } else {
                    errorCount++;
                    addToLog(`❌ ${method} ${response.status} ${url} - ${response.statusText}`);
                }
            } catch (error) {
                errorCount++;
                addToLog(`💥 ${method} request failed: ${error.message}`);
            }
            
            updateNetworkStats();
        }

        function makeGetRequest() {
            makeRequest('GET', 'https://jsonplaceholder.typicode.com/posts/1');
        }

        function makePostRequest() {
            makeRequest('POST', 'https://jsonplaceholder.typicode.com/posts', {
                title: 'BugReplay Test',
                body: 'Testing HAR data capture',
                userId: 1
            });
        }

        function makePutRequest() {
            makeRequest('PUT', 'https://jsonplaceholder.typicode.com/posts/1', {
                id: 1,
                title: 'Updated by BugReplay',
                body: 'Testing PUT request HAR capture',
                userId: 1
            });
        }

        function makeDeleteRequest() {
            makeRequest('DELETE', 'https://jsonplaceholder.typicode.com/posts/1');
        }

        function loadImage() {
            requestCount++;
            const img = new Image();
            img.onload = () => {
                successCount++;
                dataTransferred += 50000; // Approximate image size
                addToLog('🖼️ Image loaded successfully');
                updateNetworkStats();
            };
            img.onerror = () => {
                errorCount++;
                addToLog('❌ Image failed to load');
                updateNetworkStats();
            };
            img.src = 'https://picsum.photos/200/200?random=' + Date.now();
            addToLog('📡 Loading random image...');
        }

        function loadCSS() {
            requestCount++;
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap';
            link.onload = () => {
                successCount++;
                dataTransferred += 10000; // Approximate CSS size
                addToLog('🎨 CSS loaded successfully');
                updateNetworkStats();
            };
            link.onerror = () => {
                errorCount++;
                addToLog('❌ CSS failed to load');
                updateNetworkStats();
            };
            document.head.appendChild(link);
            addToLog('📡 Loading Google Fonts CSS...');
        }

        function loadScript() {
            requestCount++;
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js';
            script.onload = () => {
                successCount++;
                dataTransferred += 70000; // Approximate script size
                addToLog('📜 Script loaded successfully');
                updateNetworkStats();
            };
            script.onerror = () => {
                errorCount++;
                addToLog('❌ Script failed to load');
                updateNetworkStats();
            };
            document.head.appendChild(script);
            addToLog('📡 Loading Lodash library...');
        }

        function makeErrorRequest() {
            makeRequest('GET', 'https://httpstat.us/404');
        }

        function animateElement() {
            const target = document.getElementById('animation-target');
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            const randomSize = 50 + Math.random() * 100;
            
            target.style.background = randomColor;
            target.style.width = randomSize + 'px';
            target.style.height = randomSize + 'px';
            target.style.transform = `rotate(${Math.random() * 360}deg)`;
            
            addToLog('🎭 Element animated with new color and size');
        }

        function changeColors() {
            document.body.style.background = `linear-gradient(135deg, hsl(${Math.random() * 360}, 70%, 60%) 0%, hsl(${Math.random() * 360}, 70%, 60%) 100%)`;
            addToLog('🌈 Page colors changed');
        }

        function showModal() {
            document.getElementById('test-modal').style.display = 'block';
            addToLog('🪟 Modal dialog opened');
        }

        function hideModal() {
            document.getElementById('test-modal').style.display = 'none';
            addToLog('❌ Modal dialog closed');
        }

        function scrollToBottom() {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            addToLog('⬇️ Scrolled to bottom of page');
        }

        function addContent() {
            const content = document.getElementById('dynamic-content');
            const newElement = document.createElement('div');
            newElement.style.cssText = 'padding: 10px; margin: 5px 0; background: #e2e8f0; border-radius: 4px;';
            newElement.textContent = `Dynamic content added at ${new Date().toLocaleTimeString()}`;
            content.appendChild(newElement);
            addToLog('➕ Dynamic content added to page');
        }

        // Initialize
        addToLog('🚀 Video recording and HAR testing page loaded!');
        addToLog('📋 Start BugReplay recording, then test video and network features');
        updateNetworkStats();
    </script>
</body>
</html>
