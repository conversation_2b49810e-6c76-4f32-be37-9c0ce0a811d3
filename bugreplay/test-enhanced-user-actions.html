<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Enhanced User Action Logging Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007cba;
        }
        .form-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a87;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.danger {
            background: #dc3545;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .checkbox-group,
        .radio-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .checkbox-item,
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .navigation-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .navigation-links a {
            color: #007cba;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #007cba;
            border-radius: 4px;
        }
        .navigation-links a:hover {
            background: #007cba;
            color: white;
        }
        .selectable-text {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            user-select: text;
        }
        .drag-drop-area {
            border: 2px dashed #007cba;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .drag-drop-area.dragover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
        .icon-button {
            background: none;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .icon-button:hover {
            background: #f8f9fa;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #007cba;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        h3 {
            color: #495057;
            margin-top: 20px;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .logs {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🐛 BugReplay - Enhanced User Action Logging Test</h1>
    
    <div class="info">
        <strong>Test Instructions:</strong> This page tests the enhanced user action logging with detailed, contextual information for better reproduction steps. Start recording in the BugReplay extension and interact with the elements below to see detailed action logging.
    </div>

    <!-- Click Actions Test -->
    <div class="test-section">
        <h2>1. 🖱️ Enhanced Click Actions</h2>
        <p>Test various click interactions with descriptive context and element identification.</p>
        
        <div class="form-section">
            <h3>Buttons with Different Contexts</h3>
            <button class="button" id="primary-submit" aria-label="Submit form data">Submit Form</button>
            <button class="button secondary" title="Cancel current operation">Cancel</button>
            <button class="button danger" onclick="alert('Delete confirmed!')">Delete Item</button>
            <button class="icon-button" aria-label="Settings menu">⚙️</button>
            <button class="icon-button" title="Help and support">❓</button>
        </div>

        <div class="form-section">
            <h3>Links with Different Types</h3>
            <div class="navigation-links">
                <a href="#section1">Internal Anchor</a>
                <a href="/dashboard">Internal Page</a>
                <a href="https://example.com" target="_blank">External Link</a>
                <a href="mailto:<EMAIL>">Email Link</a>
                <a href="tel:+1234567890">Phone Link</a>
            </div>
        </div>
    </div>

    <!-- Navigation Test -->
    <div class="test-section">
        <h2>2. 🧭 Navigation/Redirections</h2>
        <p>Test page navigation events with source and destination context.</p>
        
        <div class="form-section">
            <h3>Navigation Actions</h3>
            <button class="button" onclick="window.location.hash = '#test-section'">Hash Navigation</button>
            <button class="button" onclick="history.pushState({}, '', '?test=1')">Query Parameter Change</button>
            <button class="button" onclick="document.title = 'Updated Title - ' + Date.now()">Change Page Title</button>
            <button class="button" onclick="window.open('https://example.com', '_blank')">Open External Page</button>
        </div>
    </div>

    <!-- Form Interactions Test -->
    <div class="test-section">
        <h2>3. 📝 Enhanced Form Interactions</h2>
        <p>Test comprehensive form field tracking with labels and context.</p>
        
        <form id="test-form" aria-label="User Registration Form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" placeholder="Enter your username" required>
            </div>

            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Enter secure password" required>
            </div>

            <div class="form-group">
                <label for="bio">Biography</label>
                <textarea id="bio" name="bio" rows="4" placeholder="Tell us about yourself..."></textarea>
            </div>

            <div class="form-group">
                <label for="country">Country</label>
                <select id="country" name="country">
                    <option value="">Select your country</option>
                    <option value="us">United States</option>
                    <option value="ca">Canada</option>
                    <option value="uk">United Kingdom</option>
                    <option value="de">Germany</option>
                    <option value="fr">France</option>
                </select>
            </div>

            <div class="form-group">
                <label>Interests (Checkboxes)</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="tech" name="interests" value="technology">
                        <label for="tech">Technology</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="sports" name="interests" value="sports">
                        <label for="sports">Sports</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="music" name="interests" value="music">
                        <label for="music">Music</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>Preferred Contact Method (Radio)</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" id="contact-email" name="contact_method" value="email">
                        <label for="contact-email">Email</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="contact-phone" name="contact_method" value="phone">
                        <label for="contact-phone">Phone</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="contact-sms" name="contact_method" value="sms">
                        <label for="contact-sms">SMS</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="avatar">Profile Picture</label>
                <input type="file" id="avatar" name="avatar" accept="image/*">
            </div>

            <div class="form-group">
                <label for="age">Age Range</label>
                <input type="range" id="age" name="age" min="18" max="100" value="25">
                <span id="age-display">25</span>
            </div>

            <div class="form-group">
                <label for="favorite-color">Favorite Color</label>
                <input type="color" id="favorite-color" name="favorite_color" value="#007cba">
            </div>

            <div class="form-group">
                <button type="submit" class="button">Submit Registration</button>
                <button type="reset" class="button secondary">Reset Form</button>
            </div>
        </form>
    </div>

    <!-- Selection Actions Test -->
    <div class="test-section">
        <h2>4. 🎯 Selection Actions</h2>
        <p>Test text selections, dropdown changes, and file uploads.</p>
        
        <div class="form-section">
            <h3>Text Selection</h3>
            <div class="selectable-text">
                This is a sample paragraph with selectable text. Try selecting different portions of this text to test the text selection logging functionality. The system should capture what text was selected and where it was selected from.
            </div>
        </div>

        <div class="form-section">
            <h3>Multi-Select Dropdown</h3>
            <label for="skills">Skills (Multi-select)</label>
            <select id="skills" name="skills" multiple size="5">
                <option value="javascript">JavaScript</option>
                <option value="python">Python</option>
                <option value="java">Java</option>
                <option value="csharp">C#</option>
                <option value="php">PHP</option>
                <option value="ruby">Ruby</option>
            </select>
        </div>

        <div class="form-section">
            <h3>Drag and Drop File Upload</h3>
            <div class="drag-drop-area" id="drop-zone">
                <p>📁 Drag and drop files here or click to select</p>
                <input type="file" id="file-upload" multiple style="display: none;">
                <button class="button" onclick="document.getElementById('file-upload').click()">Choose Files</button>
            </div>
        </div>
    </div>

    <!-- Test Logs -->
    <div class="test-section">
        <h2>📊 Action Logs</h2>
        <p>Monitor the enhanced user action logging in real-time:</p>
        <div id="action-logs" class="logs">
            Action logs will appear here when BugReplay recording is active...
        </div>
        <button class="button secondary" onclick="clearActionLogs()">Clear Logs</button>
    </div>

    <div id="test-section" style="margin-top: 50px;">
        <h3>Test Section (for hash navigation)</h3>
        <p>This section is used for testing hash navigation functionality.</p>
    </div>

    <script>
        // Enhanced logging for demonstration
        function logAction(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('action-logs');
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearActionLogs() {
            document.getElementById('action-logs').textContent = 'Action logs cleared...\n';
        }

        // Age range slider update
        document.getElementById('age').addEventListener('input', function(e) {
            document.getElementById('age-display').textContent = e.target.value;
        });

        // Drag and drop functionality
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-upload');

        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                logAction(`Files dropped: ${Array.from(files).map(f => f.name).join(', ')}`);
            }
        });

        // File input change
        fileInput.addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                logAction(`Files selected: ${Array.from(files).map(f => f.name).join(', ')}`);
            }
        });

        // Form submission handler
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            logAction('Form submission prevented for testing purposes');
            alert('Form submission captured! Check the BugReplay logs for detailed form interaction tracking.');
        });

        // Text selection monitoring
        document.addEventListener('selectionchange', function() {
            const selection = window.getSelection();
            if (selection && !selection.isCollapsed && selection.toString().trim().length > 0) {
                const selectedText = selection.toString().trim();
                logAction(`Text selected: "${selectedText.length > 50 ? selectedText.substring(0, 47) + '...' : selectedText}"`);
            }
        });

        // Initialize
        logAction('Enhanced user action logging test page loaded');
        logAction('Start BugReplay recording to see detailed action tracking');
    </script>
</body>
</html>
