<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            margin-bottom: 20px;
        }
        
        .test-step {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        
        .test-step.completed {
            border-left-color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }
        
        .test-step.failed {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
        
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .action-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 BugReplay Persistence Test</h1>
        <p>This page helps you test the persistence functionality of the BugReplay extension.</p>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <p><strong>Goal:</strong> Verify that recording state and data persist when the popup is closed and reopened.</p>
        </div>
        
        <div class="test-step" id="step1">
            <span class="step-number">1</span>
            <strong>Open BugReplay Extension</strong>
            <p>Click the BugReplay icon in your browser toolbar to open the popup.</p>
        </div>
        
        <div class="test-step" id="step2">
            <span class="step-number">2</span>
            <strong>Start Recording</strong>
            <p>Click "Start Recording" in the extension popup. You should see recording status change to active.</p>
        </div>
        
        <div class="test-step" id="step3">
            <span class="step-number">3</span>
            <strong>Perform Actions</strong>
            <p>Click the buttons below to generate some test actions:</p>
            <button onclick="performAction('Button Click 1')">Test Action 1</button>
            <button onclick="performAction('Button Click 2')" class="action-button">Test Action 2</button>
            <button onclick="generateError()">Generate Error</button>
        </div>
        
        <div class="test-step" id="step4">
            <span class="step-number">4</span>
            <strong>Close Extension Popup</strong>
            <p>Close the BugReplay extension popup by clicking outside it or pressing Escape.</p>
        </div>
        
        <div class="test-step" id="step5">
            <span class="step-number">5</span>
            <strong>Perform More Actions</strong>
            <p>With the popup closed, perform more actions on this page:</p>
            <button onclick="performAction('Action After Close 1')">Post-Close Action 1</button>
            <button onclick="performAction('Action After Close 2')">Post-Close Action 2</button>
            <button onclick="makeNetworkRequest()">Network Request</button>
        </div>
        
        <div class="test-step" id="step6">
            <span class="step-number">6</span>
            <strong>Reopen Extension Popup</strong>
            <p>Click the BugReplay icon again to reopen the popup.</p>
            <p><strong>Expected Result:</strong> The extension should show "recording in progress" and display all captured logs.</p>
        </div>
        
        <div class="test-step" id="step7">
            <span class="step-number">7</span>
            <strong>Stop Recording</strong>
            <p>Click "Stop Recording" in the extension popup.</p>
            <p><strong>Expected Result:</strong> A bug report should be generated with all actions from steps 3 and 5.</p>
        </div>
        
        <div class="test-step" id="step8">
            <span class="step-number">8</span>
            <strong>Verify Bug Report</strong>
            <p>Check that the bug report contains:</p>
            <ul>
                <li>All button clicks from before and after popup closure</li>
                <li>Network requests and console logs</li>
                <li>Screenshots and video recording</li>
                <li>Complete session timeline</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 Test Activity Log</h2>
        <div id="activityLog" class="log">
            Test activity will be logged here...
        </div>
        <button onclick="clearTestLog()">Clear Log</button>
    </div>
    
    <div class="test-container">
        <h2>🔧 Additional Test Actions</h2>
        <p>Use these actions to generate more test data:</p>
        
        <button onclick="scrollTest()">Scroll Test</button>
        <button onclick="formTest()">Form Interaction</button>
        <button onclick="domManipulation()">DOM Changes</button>
        <button onclick="consoleTest()">Console Messages</button>
        
        <div id="dynamicContent" style="margin-top: 20px; min-height: 50px; border: 2px dashed rgba(255,255,255,0.3); border-radius: 8px; padding: 15px;">
            Dynamic content area...
        </div>
        
        <form id="testForm" style="margin-top: 20px;">
            <input type="text" placeholder="Test input field" style="padding: 8px; margin: 5px; border-radius: 4px; border: none;">
            <select style="padding: 8px; margin: 5px; border-radius: 4px; border: none;">
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
            </select>
            <button type="submit" style="margin: 5px;">Submit Form</button>
        </form>
    </div>

    <script>
        let actionCount = 0;
        
        function logTestActivity(message) {
            actionCount++;
            const log = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${actionCount}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`BugReplay Persistence Test: ${message}`);
        }
        
        function performAction(actionName) {
            logTestActivity(`Performed action: ${actionName}`);
            console.log(`Test action performed: ${actionName}`);
        }
        
        function generateError() {
            logTestActivity('Generating test error...');
            try {
                // This will throw a ReferenceError
                undefinedFunction();
            } catch (error) {
                logTestActivity(`Error generated: ${error.message}`);
                console.error('Test error:', error);
            }
        }
        
        async function makeNetworkRequest() {
            logTestActivity('Making test network request...');
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
                const data = await response.json();
                logTestActivity(`Network request completed: ${response.status}`);
                console.log('Network response:', data);
            } catch (error) {
                logTestActivity(`Network request failed: ${error.message}`);
                console.error('Network error:', error);
            }
        }
        
        function scrollTest() {
            logTestActivity('Performing scroll test...');
            window.scrollTo({ top: window.scrollY + 200, behavior: 'smooth' });
        }
        
        function formTest() {
            logTestActivity('Performing form interaction test...');
            const input = document.querySelector('input[type="text"]');
            input.value = `Test value ${Date.now()}`;
            input.focus();
        }
        
        function domManipulation() {
            logTestActivity('Performing DOM manipulation test...');
            const content = document.getElementById('dynamicContent');
            const newElement = document.createElement('div');
            newElement.innerHTML = `<p style="background: rgba(255,255,255,0.1); padding: 10px; margin: 5px; border-radius: 5px;">Dynamic element added at ${new Date().toLocaleTimeString()}</p>`;
            content.appendChild(newElement);
        }
        
        function consoleTest() {
            logTestActivity('Generating console messages...');
            console.log('Test console log message');
            console.warn('Test console warning message');
            console.info('Test console info message');
        }
        
        function clearTestLog() {
            document.getElementById('activityLog').innerHTML = '';
            actionCount = 0;
            logTestActivity('Test log cleared');
        }
        
        // Form submission handler
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            logTestActivity('Form submitted');
            console.log('Test form submitted');
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logTestActivity('BugReplay persistence test page loaded');
            console.log('BugReplay Persistence Test: Page ready');
        });
        
        // Track page interactions
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                logTestActivity(`Button clicked: ${e.target.textContent}`);
            }
        });
        
        window.addEventListener('scroll', function() {
            logTestActivity(`Page scrolled to: ${window.pageYOffset}`);
        });
    </script>
</body>
</html>
