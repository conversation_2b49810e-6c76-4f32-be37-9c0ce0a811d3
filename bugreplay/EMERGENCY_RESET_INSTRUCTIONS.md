# BugReplay - Emergency Reset Instructions

## 🚨 **IMMEDIATE SOLUTION for "Recording already in progress" Error**

If you're seeing the "Recording already in progress" error immediately when clicking start recording, here are **3 ways** to fix it:

---

## **Method 1: Use the Emergency Reset Panel (Recommended)**

1. **Open the BugReplay extension popup**
2. **Look for the red "🚨 Recording Issues?" panel at the top**
3. **Click "🔄 Emergency Reset" button**
4. **Wait for "Emergency reset successful!" message**
5. **Try starting recording again**

---

## **Method 2: Browser Console Reset (If UI doesn't work)**

1. **Open Chrome DevTools** (F12 or right-click → Inspect)
2. **Go to the Console tab**
3. **Paste this code and press Enter:**

```javascript
// Emergency BugReplay Reset Script
(function() {
    console.log('🚨 BugReplay Emergency Reset Starting...');
    
    // Method 1: Try extension API reset
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({ type: 'FORCE_RESET_RECORDING' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('❌ Extension reset failed:', chrome.runtime.lastError.message);
                console.log('🔄 Trying storage cleanup...');
                
                // Method 2: Clear extension storage
                chrome.storage.local.clear(() => {
                    console.log('✅ Extension storage cleared');
                    console.log('🔄 Please reload the page and try again');
                });
            } else if (response && response.success) {
                console.log('✅ Extension reset successful!');
                console.log('🎯 You can now start recording');
            } else {
                console.error('❌ Extension reset failed:', response?.error);
            }
        });
    } else {
        console.log('❌ Extension API not available');
    }
    
    // Method 3: Clear any stuck content script state
    if (typeof window.isCapturing !== 'undefined') {
        window.isCapturing = false;
        console.log('✅ Content script state reset');
    }
    
    console.log('🔄 Emergency reset completed. Try starting recording now.');
})();
```

---

## **Method 3: Manual Extension Reset**

1. **Go to Chrome Extensions page** (`chrome://extensions/`)
2. **Find "BugReplay" extension**
3. **Click the "Reload" button** (🔄 icon)
4. **Go back to your page and try recording again**

---

## **Method 4: Complete Reset (Nuclear Option)**

If all else fails:

1. **Go to Chrome Extensions** (`chrome://extensions/`)
2. **Turn OFF the BugReplay extension**
3. **Wait 5 seconds**
4. **Turn ON the BugReplay extension**
5. **Reload your webpage**
6. **Try starting recording**

---

## **Prevention Tips**

To avoid this issue in the future:

### **✅ Do:**
- Always use the "Stop Recording" button before closing tabs
- Wait for recording to fully stop before starting a new one
- Use the "Check State" button if unsure about recording status

### **❌ Don't:**
- Close tabs while recording is active
- Click "Start Recording" multiple times rapidly
- Force-close Chrome while recording

---

## **Debug Information**

If you continue having issues, please provide this information:

### **Check Current State:**
1. Open BugReplay extension
2. Click "Check State" button
3. Note the debug information shown

### **Console Logs:**
1. Open Chrome DevTools (F12)
2. Go to Console tab
3. Look for messages starting with "BugReplay:"
4. Copy any error messages

---

## **Technical Details**

### **What Causes This Error:**
- Extension state becomes stuck in "RECORDING" mode
- Previous recording session didn't clean up properly
- Browser crash or force-close during recording
- Extension update while recording was active

### **How the Fix Works:**
- **Force Reset**: Clears all recording state and storage
- **State Validation**: Checks for inconsistencies and auto-corrects
- **Clean Shutdown**: Properly stops all recording components

---

## **Contact Support**

If none of these methods work:

1. **Try the emergency reset script** (Method 2) first
2. **Restart Chrome completely**
3. **Check if the issue persists**
4. **Report the issue** with debug information

---

## **Quick Reference Card**

```
🚨 EMERGENCY RESET STEPS:
1. Open BugReplay popup
2. Click "🔄 Emergency Reset"
3. Wait for success message
4. Try recording again

🔧 CONSOLE RESET:
1. Press F12
2. Go to Console
3. Paste reset script
4. Press Enter

🔄 EXTENSION RELOAD:
1. Go to chrome://extensions/
2. Find BugReplay
3. Click reload button
4. Try again
```

The emergency reset functionality should resolve 99% of "Recording already in progress" errors immediately. The new UI panel makes it easy to access and provides clear feedback about the reset process.
