<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section h2 {
            margin-top: 0;
            color: #ffd700;
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }
        
        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .dynamic-content {
            min-height: 100px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .error-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .network-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }
        
        .success-button {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 BugReplay Test Page</h1>
        <p style="text-align: center; margin-bottom: 30px;">
            Use this page to test the BugReplay Chrome extension's recording capabilities.
            Start recording in the extension, then interact with the elements below.
        </p>
        
        <div class="section">
            <h2>🖱️ Click Interactions</h2>
            <button onclick="handleClick('Button 1')">Click Me - Button 1</button>
            <button onclick="handleClick('Button 2')">Click Me - Button 2</button>
            <button onclick="addDynamicContent()">Add Dynamic Content</button>
            <button onclick="removeDynamicContent()">Remove Content</button>
        </div>
        
        <div class="section">
            <h2>📝 Form Interactions</h2>
            <form onsubmit="handleFormSubmit(event)">
                <input type="text" placeholder="Enter your name" id="nameInput">
                <input type="email" placeholder="Enter your email" id="emailInput">
                <select id="categorySelect">
                    <option value="">Select a category</option>
                    <option value="bug">Bug Report</option>
                    <option value="feature">Feature Request</option>
                    <option value="question">Question</option>
                </select>
                <textarea placeholder="Enter your message" id="messageTextarea" rows="4"></textarea>
                <button type="submit" class="success-button">Submit Form</button>
            </form>
        </div>
        
        <div class="section">
            <h2>🌐 Network Requests</h2>
            <button onclick="makeSuccessfulRequest()" class="success-button">Successful API Call</button>
            <button onclick="makeFailedRequest()" class="error-button">Failed API Call</button>
            <button onclick="makeSlowRequest()" class="network-button">Slow API Call</button>
        </div>
        
        <div class="section">
            <h2>❌ Error Generation</h2>
            <button onclick="generateJSError()" class="error-button">Generate JS Error</button>
            <button onclick="generateConsoleError()" class="error-button">Console Error</button>
            <button onclick="generateWarning()" style="background: linear-gradient(45deg, #f39c12, #e67e22);">Generate Warning</button>
        </div>
        
        <div class="section">
            <h2>🔄 Dynamic Content</h2>
            <div id="dynamicContent" class="dynamic-content">
                Dynamic content will appear here...
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Activity Log</h2>
            <div id="activityLog" class="log">
                Activity log will appear here...
            </div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="section">
            <h2>📜 Scroll Test</h2>
            <div style="height: 300px; overflow-y: auto; border: 1px solid rgba(255,255,255,0.3); border-radius: 8px; padding: 15px; background: rgba(0,0,0,0.2);">
                <p>Scroll within this area to test scroll tracking:</p>
                <div style="height: 800px; background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(255,255,255,0.05)); border-radius: 8px; padding: 20px;">
                    <h3>Scrollable Content</h3>
                    <p>This is a long scrollable section to test scroll event capture.</p>
                    <p>Keep scrolling to see more content...</p>
                    <p>Line 1 of scrollable content</p>
                    <p>Line 2 of scrollable content</p>
                    <p>Line 3 of scrollable content</p>
                    <p>Line 4 of scrollable content</p>
                    <p>Line 5 of scrollable content</p>
                    <p>Line 6 of scrollable content</p>
                    <p>Line 7 of scrollable content</p>
                    <p>Line 8 of scrollable content</p>
                    <p>Line 9 of scrollable content</p>
                    <p>Line 10 of scrollable content</p>
                    <p>You've reached the end of the scrollable content!</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let activityCount = 0;
        
        function logActivity(message) {
            activityCount++;
            const log = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${activityCount}: ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`BugReplay Test: ${message}`);
        }
        
        function handleClick(buttonName) {
            logActivity(`Clicked ${buttonName}`);
            console.log(`Button clicked: ${buttonName}`);
        }
        
        function addDynamicContent() {
            const container = document.getElementById('dynamicContent');
            const newElement = document.createElement('div');
            newElement.innerHTML = `
                <p style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 5px 0;">
                    Dynamic element added at ${new Date().toLocaleTimeString()}
                    <button onclick="this.parentElement.remove(); logActivity('Dynamic element removed');" style="margin-left: 10px; padding: 5px 10px; font-size: 12px;">Remove</button>
                </p>
            `;
            container.appendChild(newElement);
            logActivity('Dynamic content added to DOM');
        }
        
        function removeDynamicContent() {
            const container = document.getElementById('dynamicContent');
            if (container.children.length > 0) {
                container.removeChild(container.lastElementChild);
                logActivity('Dynamic content removed from DOM');
            } else {
                logActivity('No dynamic content to remove');
            }
        }
        
        function handleFormSubmit(event) {
            event.preventDefault();
            const name = document.getElementById('nameInput').value;
            const email = document.getElementById('emailInput').value;
            const category = document.getElementById('categorySelect').value;
            const message = document.getElementById('messageTextarea').value;
            
            logActivity(`Form submitted: Name=${name}, Email=${email}, Category=${category}`);
            console.log('Form data:', { name, email, category, message });
            
            // Clear form
            event.target.reset();
        }
        
        async function makeSuccessfulRequest() {
            logActivity('Making successful API request...');
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
                const data = await response.json();
                logActivity(`Successful API response received (Status: ${response.status})`);
                console.log('API Response:', data);
            } catch (error) {
                logActivity(`API request failed: ${error.message}`);
                console.error('API Error:', error);
            }
        }
        
        async function makeFailedRequest() {
            logActivity('Making failed API request...');
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/999999');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                logActivity(`Unexpected success: ${response.status}`);
            } catch (error) {
                logActivity(`Expected API failure: ${error.message}`);
                console.error('Expected API Error:', error);
            }
        }
        
        async function makeSlowRequest() {
            logActivity('Making slow API request...');
            try {
                // Simulate slow request with delay
                const controller = new AbortController();
                setTimeout(() => controller.abort(), 5000);
                
                const response = await fetch('https://httpbin.org/delay/3', {
                    signal: controller.signal
                });
                const data = await response.json();
                logActivity(`Slow API request completed (Status: ${response.status})`);
                console.log('Slow API Response:', data);
            } catch (error) {
                logActivity(`Slow API request failed: ${error.message}`);
                console.error('Slow API Error:', error);
            }
        }
        
        function generateJSError() {
            logActivity('Generating JavaScript error...');
            try {
                // This will throw a ReferenceError
                nonExistentFunction();
            } catch (error) {
                logActivity(`JavaScript error generated: ${error.message}`);
                console.error('Generated JS Error:', error);
                throw error; // Re-throw to ensure it appears in console
            }
        }
        
        function generateConsoleError() {
            logActivity('Generating console error...');
            console.error('This is a test console error generated by BugReplay test page');
            console.error('Error object:', new Error('Test error with stack trace'));
        }
        
        function generateWarning() {
            logActivity('Generating console warning...');
            console.warn('This is a test console warning generated by BugReplay test page');
            console.warn('Warning with object:', { type: 'test', severity: 'medium' });
        }
        
        function clearLog() {
            document.getElementById('activityLog').innerHTML = '';
            activityCount = 0;
            logActivity('Activity log cleared');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logActivity('BugReplay test page loaded and ready');
            console.log('BugReplay Test Page: Ready for recording');
        });
        
        // Track scroll events on the page
        window.addEventListener('scroll', function() {
            logActivity(`Page scrolled to position: ${window.pageYOffset}`);
        });
        
        // Track keyboard events
        document.addEventListener('keydown', function(event) {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                logActivity(`Key pressed: ${event.key} in ${event.target.tagName.toLowerCase()}`);
            }
        });
    </script>
</body>
</html>
