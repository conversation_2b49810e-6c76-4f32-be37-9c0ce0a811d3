{"name": "bugreplay-chrome-extension", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.2", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "copy-vite-plugin": "^1.0.1", "postcss": "^8.5.6", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "vite": "^5.4.19"}, "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production && open dist/bundle-analysis.html", "build:clean": "rm -rf dist node_modules package-lock.json && npm install && npm run build", "build:extension": "npm run build:prod && echo '✅ Extension built successfully! Load the dist/ folder in Chrome Extensions.'", "preview": "vite preview", "watch": "vite build --watch", "clean": "rm -rf dist"}}