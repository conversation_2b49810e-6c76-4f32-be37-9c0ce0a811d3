<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Three Key Improvements Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .improvement {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007cba;
        }
        .test-section {
            background: #f9f9f9;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a87;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #545b62;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f0f0f0;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .logs {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        .network-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .network-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #007cba;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        h3 {
            color: #495057;
            margin-top: 20px;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            color: #007cba;
            font-weight: bold;
        }
        .checklist li.completed:before {
            content: "✅ ";
        }
    </style>
</head>
<body>
    <h1>🐛 BugReplay - Three Key Improvements Test</h1>
    
    <div class="info status">
        <strong>Test Instructions:</strong> This page validates the three specific improvements made to BugReplay:
        <ol>
            <li>Automatic video recording (no manual button)</li>
            <li>Enhanced HAR file export quality</li>
            <li>Settings tab with configuration options</li>
        </ol>
    </div>

    <!-- Improvement 1: Automatic Video Recording -->
    <div class="improvement">
        <h2>1. 🎥 Automatic Video Recording</h2>
        <p><strong>Changes Made:</strong> Removed manual "Start Video" button, video recording now starts automatically with recording sessions.</p>
        
        <div class="test-section">
            <h3>Test Steps:</h3>
            <ul class="checklist">
                <li>Open BugReplay extension popup</li>
                <li>Verify no "Start Video" button is visible in Recording tab</li>
                <li>Start a recording session</li>
                <li>Verify video recording status shows "Video Recording (Automatic)"</li>
                <li>Stop recording session</li>
                <li>Verify video recording stops automatically</li>
            </ul>
            
            <button class="button" onclick="testVideoRecording()">Test Video Recording</button>
            <div id="video-status" class="status">
                Click "Test Video Recording" to validate automatic video recording behavior...
            </div>
        </div>
    </div>

    <!-- Improvement 2: Enhanced HAR File Quality -->
    <div class="improvement">
        <h2>2. 📊 Enhanced HAR File Export Quality</h2>
        <p><strong>Changes Made:</strong> Improved network monitoring with complete headers, timing, redirects, and Chrome DevTools compatibility.</p>
        
        <div class="test-section">
            <h3>Network Test Requests:</h3>
            <div class="network-test">
                <div class="network-item">
                    <button class="button" onclick="makeRequest('GET', 'https://httpbin.org/get')">GET Request</button>
                </div>
                <div class="network-item">
                    <button class="button" onclick="makeRequest('POST', 'https://httpbin.org/post')">POST Request</button>
                </div>
                <div class="network-item">
                    <button class="button" onclick="makeRequest('PUT', 'https://httpbin.org/put')">PUT Request</button>
                </div>
                <div class="network-item">
                    <button class="button" onclick="makeRequest('DELETE', 'https://httpbin.org/delete')">DELETE Request</button>
                </div>
                <div class="network-item">
                    <button class="button" onclick="makeRedirectRequest()">Redirect Test</button>
                </div>
                <div class="network-item">
                    <button class="button" onclick="makeErrorRequest()">Error Test</button>
                </div>
            </div>
            
            <h3>HAR Quality Tests:</h3>
            <ul class="checklist">
                <li>Start recording session</li>
                <li>Make various network requests above</li>
                <li>Stop recording and generate bug report</li>
                <li>Download HAR file from bug report</li>
                <li>Import HAR file into Chrome DevTools Network tab</li>
                <li>Verify complete headers, timing, and request/response data</li>
            </ul>
            
            <button class="button" onclick="testHarQuality()">Test HAR Quality</button>
            <div id="har-status" class="status">
                Click "Test HAR Quality" to validate enhanced network monitoring...
            </div>
        </div>
    </div>

    <!-- Improvement 3: Settings Tab -->
    <div class="improvement">
        <h2>3. ⚙️ Settings Tab with Configuration Options</h2>
        <p><strong>Changes Made:</strong> Added Settings tab with toggles for automatic screenshots, video recording, network monitoring, and video save location.</p>
        
        <div class="test-section">
            <h3>Settings Features to Test:</h3>
            <ul class="checklist">
                <li>Open BugReplay extension popup</li>
                <li>Click on "Settings" tab (should be visible next to Recording and Sessions)</li>
                <li>Verify "Automatic Screenshot Capture" toggle</li>
                <li>Verify "Video Recording" toggle</li>
                <li>Verify "Network Monitoring" toggle</li>
                <li>Verify "Video Save Location" setting with Browse button</li>
                <li>Test toggling settings and verify they save automatically</li>
                <li>Verify settings persist after closing/reopening popup</li>
                <li>Test "Reset to Defaults" button</li>
            </ul>
            
            <button class="button" onclick="testSettings()">Test Settings Tab</button>
            <div id="settings-status" class="status">
                Click "Test Settings Tab" to validate configuration options...
            </div>
        </div>
    </div>

    <!-- Test Logs -->
    <div class="improvement">
        <h2>📋 Test Logs</h2>
        <div id="test-logs" class="logs">
            Test logs will appear here...
        </div>
        <button class="button secondary" onclick="clearLogs()">Clear Logs</button>
        <button class="button" onclick="runAllTests()">Run All Tests</button>
    </div>

    <script>
        // Logging utility
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`BugReplay Test: ${logEntry.trim()}`);
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = 'Test logs cleared...\n';
        }

        // Test 1: Video Recording
        function testVideoRecording() {
            log('Testing automatic video recording...');
            
            const statusDiv = document.getElementById('video-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                statusDiv.innerHTML = '<div class="info">✅ Extension detected. Check popup for automatic video recording behavior.</div>';
                log('Extension API available - manual verification required in popup', 'success');
            } else {
                statusDiv.innerHTML = '<div class="warning">⚠️ Extension not detected. Load extension and test manually.</div>';
                log('Extension API not available - load extension first', 'warning');
            }
        }

        // Test 2: HAR Quality
        function testHarQuality() {
            log('Testing HAR file quality...');
            
            const statusDiv = document.getElementById('har-status');
            statusDiv.innerHTML = '<div class="info">📊 HAR quality test initiated. Make network requests and check recording.</div>';
            log('HAR quality test started - make network requests during recording', 'info');
        }

        // Network request helpers
        async function makeRequest(method, url) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Test-Header': 'BugReplay-HAR-Test',
                        'User-Agent': 'BugReplay-Extension-Test'
                    }
                };
                
                if (method === 'POST' || method === 'PUT') {
                    options.body = JSON.stringify({
                        test: 'BugReplay HAR quality test',
                        timestamp: new Date().toISOString(),
                        method: method
                    });
                }
                
                const response = await fetch(url, options);
                const data = await response.text();
                
                log(`${method} request to ${url}: ${response.status} ${response.statusText}`, 'info');
                return response;
            } catch (error) {
                log(`${method} request failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function makeRedirectRequest() {
            try {
                const response = await fetch('https://httpbin.org/redirect/3');
                log(`Redirect test completed: ${response.status} ${response.statusText}`, 'info');
            } catch (error) {
                log(`Redirect test failed: ${error.message}`, 'error');
            }
        }

        async function makeErrorRequest() {
            try {
                const response = await fetch('https://httpbin.org/status/500');
                log(`Error test completed: ${response.status} ${response.statusText}`, 'info');
            } catch (error) {
                log(`Error test failed: ${error.message}`, 'error');
            }
        }

        // Test 3: Settings
        function testSettings() {
            log('Testing settings tab...');
            
            const statusDiv = document.getElementById('settings-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                statusDiv.innerHTML = '<div class="info">⚙️ Extension detected. Open popup and navigate to Settings tab.</div>';
                log('Extension API available - check Settings tab in popup', 'success');
            } else {
                statusDiv.innerHTML = '<div class="warning">⚠️ Extension not detected. Load extension and test manually.</div>';
                log('Extension API not available - load extension first', 'warning');
            }
        }

        // Run all tests
        function runAllTests() {
            log('Running comprehensive test suite...', 'info');
            
            setTimeout(() => testVideoRecording(), 500);
            setTimeout(() => testHarQuality(), 1000);
            setTimeout(() => testSettings(), 1500);
            
            log('All tests initiated - manual verification required in extension popup', 'success');
        }

        // Initialize
        log('BugReplay improvements test page loaded');
        log('Ready to test three key improvements');
        
        // Auto-detect extension
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            log('Chrome extension API detected', 'success');
        } else {
            log('Chrome extension API not detected - load BugReplay extension first', 'warning');
        }
    </script>
</body>
</html>
