import React, { useState, useEffect } from 'react';

/**
 * Settings component for configuring BugReplay extension options
 */
export const Settings = () => {
  const [settings, setSettings] = useState({
    automaticScreenshots: true,
    videoRecording: true,
    networkMonitoring: true,
    videoSaveLocation: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);

  useEffect(() => {
    loadSettings();
  }, []);

  /**
   * Load settings from Chrome storage
   */
  const loadSettings = async () => {
    setLoading(true);
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        chrome.storage.sync.get(['bugReplaySettings'], (result) => {
          if (result.bugReplaySettings) {
            setSettings(prev => ({ ...prev, ...result.bugReplaySettings }));
          }
          setLoading(false);
        });
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setLoading(false);
    }
  };

  /**
   * Save settings to Chrome storage
   */
  const saveSettings = async (newSettings) => {
    setSaving(true);
    setSaveStatus(null);
    
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        chrome.storage.sync.set({ bugReplaySettings: newSettings }, () => {
          if (chrome.runtime.lastError) {
            setSaveStatus({ type: 'error', message: 'Failed to save settings' });
          } else {
            setSaveStatus({ type: 'success', message: 'Settings saved successfully' });
            
            // Notify background script of settings change
            chrome.runtime.sendMessage({
              type: 'SETTINGS_UPDATED',
              settings: newSettings
            });
          }
          setSaving(false);
          
          // Clear status after 3 seconds
          setTimeout(() => setSaveStatus(null), 3000);
        });
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveStatus({ type: 'error', message: 'Failed to save settings' });
      setSaving(false);
    }
  };

  /**
   * Handle setting toggle
   */
  const handleToggle = (settingKey) => {
    const newSettings = {
      ...settings,
      [settingKey]: !settings[settingKey]
    };
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  /**
   * Handle directory selection for video save location
   */
  const handleSelectDirectory = async () => {
    try {
      // Use the File System Access API if available
      if ('showDirectoryPicker' in window) {
        const directoryHandle = await window.showDirectoryPicker();
        const newSettings = {
          ...settings,
          videoSaveLocation: directoryHandle.name
        };
        setSettings(newSettings);
        saveSettings(newSettings);
      } else {
        // Fallback: show info about manual configuration
        setSaveStatus({ 
          type: 'info', 
          message: 'Directory picker not available. Videos will save to default download folder.' 
        });
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error selecting directory:', error);
        setSaveStatus({ type: 'error', message: 'Failed to select directory' });
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
        <span className="ml-2 text-slate-400">Loading settings...</span>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-bold text-white mb-2">Settings</h2>
        <p className="text-slate-400 text-sm">Configure BugReplay recording options</p>
      </div>

      {/* Save Status */}
      {saveStatus && (
        <div className={`p-3 rounded-md text-sm ${
          saveStatus.type === 'success' 
            ? 'bg-green-800 text-green-200 border border-green-600'
            : saveStatus.type === 'error'
            ? 'bg-red-800 text-red-200 border border-red-600'
            : 'bg-blue-800 text-blue-200 border border-blue-600'
        }`}>
          {saveStatus.message}
        </div>
      )}

      {/* Recording Features */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-lg font-semibold text-white mb-4">Recording Features</h3>
        
        <div className="space-y-4">
          {/* Automatic Screenshots */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-white font-medium">Automatic Screenshot Capture</label>
              <p className="text-slate-400 text-sm">Capture screenshots automatically during recording</p>
            </div>
            <button
              onClick={() => handleToggle('automaticScreenshots')}
              disabled={saving}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings.automaticScreenshots ? 'bg-indigo-600' : 'bg-slate-600'
              } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings.automaticScreenshots ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Video Recording */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-white font-medium">Video Recording</label>
              <p className="text-slate-400 text-sm">Enable automatic video recording during sessions</p>
            </div>
            <button
              onClick={() => handleToggle('videoRecording')}
              disabled={saving}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings.videoRecording ? 'bg-indigo-600' : 'bg-slate-600'
              } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings.videoRecording ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Network Monitoring */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-white font-medium">Network Monitoring</label>
              <p className="text-slate-400 text-sm">Capture network requests and generate HAR files</p>
            </div>
            <button
              onClick={() => handleToggle('networkMonitoring')}
              disabled={saving}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings.networkMonitoring ? 'bg-indigo-600' : 'bg-slate-600'
              } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings.networkMonitoring ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Video Settings */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-lg font-semibold text-white mb-4">Video Settings</h3>
        
        <div className="space-y-4">
          {/* Video Save Location */}
          <div>
            <label className="text-white font-medium block mb-2">Video Save Location</label>
            <p className="text-slate-400 text-sm mb-3">Choose where screen recordings will be saved</p>
            
            <div className="flex items-center gap-3">
              <div className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-300 text-sm">
                {settings.videoSaveLocation || 'Default download directory'}
              </div>
              <button
                onClick={handleSelectDirectory}
                disabled={saving}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-500 disabled:bg-indigo-800 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors"
              >
                Browse
              </button>
            </div>
            
            <p className="text-slate-500 text-xs mt-2">
              Note: Directory selection may not be available in all browsers. Videos will save to your default download folder.
            </p>
          </div>
        </div>
      </div>

      {/* Reset Settings */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-lg font-semibold text-white mb-4">Reset</h3>
        
        <div className="flex items-center justify-between">
          <div>
            <label className="text-white font-medium">Reset to Defaults</label>
            <p className="text-slate-400 text-sm">Restore all settings to their default values</p>
          </div>
          <button
            onClick={() => {
              const defaultSettings = {
                automaticScreenshots: true,
                videoRecording: true,
                networkMonitoring: true,
                videoSaveLocation: ''
              };
              setSettings(defaultSettings);
              saveSettings(defaultSettings);
            }}
            disabled={saving}
            className="px-4 py-2 bg-red-600 hover:bg-red-500 disabled:bg-red-800 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors"
          >
            Reset
          </button>
        </div>
      </div>

      {/* Info */}
      <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
        <p className="text-slate-400 text-sm">
          💡 <strong>Tip:</strong> Settings are automatically saved and will be applied to new recording sessions. 
          Changes to video recording and network monitoring require starting a new session to take effect.
        </p>
      </div>
    </div>
  );
};
