
import React from 'react';
import { RecordIcon } from './icons/RecordIcon.jsx';
import { StopIcon } from './icons/StopIcon.jsx';
import { ErrorIcon } from './icons/ErrorIcon.jsx';

/**
 * Controls component for recording and error simulation
 * @param {Object} props - Component props
 * @param {boolean} props.isRecording - Whether recording is active
 * @param {Function} props.onStartRecording - Start recording callback
 * @param {Function} props.onStopRecording - Stop recording callback
 * @param {Function} props.onSimulateJSError - Simulate JS error callback
 * @param {Function} props.onSimulateNetworkError - Simulate network error callback
 * @param {Function} props.onCaptureScreenshot - Capture manual screenshot callback
 * @param {boolean} props.capturingScreenshot - Whether screenshot capture is in progress
 * @param {Function} props.onStartVideoRecording - Start video recording callback
 * @param {boolean} props.videoRecording - Whether video recording is active
 * @returns {JSX.Element} The controls component
 */
export const Controls = ({
  isRecording,
  onStartRecording,
  onStopRecording,
  onSimulateJSError,
  onSimulateNetworkError,
  onCaptureScreenshot,
  capturingScreenshot = false,
  onStartVideoRecording,
  videoRecording = false,
}) => {
  return (
    <div className="mb-4 p-3 bg-slate-700/50 rounded-lg shadow-md">
      <div className="flex flex-col space-y-2">
        {!isRecording ? (
          <button
            onClick={onStartRecording}
            className="flex items-center justify-center w-full px-4 py-2 bg-green-600 hover:bg-green-500 text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75"
          >
            <RecordIcon className="w-4 h-4 mr-2" />
            Start Recording
          </button>
        ) : (
          <div className="space-y-2">
            <button
              onClick={onStopRecording}
              className="flex items-center justify-center w-full px-4 py-2 bg-red-600 hover:bg-red-500 text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-75"
            >
              <StopIcon className="w-4 h-4 mr-2" />
              Stop Recording
              <span className="ml-2 w-2 h-2 bg-white rounded-full animate-ping"></span>
            </button>
            <button
              onClick={onCaptureScreenshot}
              disabled={capturingScreenshot}
              className="flex items-center justify-center w-full px-4 py-2 bg-blue-600 hover:bg-blue-500 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75"
            >
              {capturingScreenshot ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Capturing...
                </>
              ) : (
                <>
                  📸 Take Screenshot
                </>
              )}
            </button>
            {!videoRecording && (
              <button
                onClick={onStartVideoRecording}
                className="flex items-center justify-center w-full px-4 py-2 bg-purple-600 hover:bg-purple-500 text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-75"
              >
                🎥 Start Video Recording
              </button>
            )}
            {videoRecording && (
              <div className="flex items-center justify-center w-full px-4 py-2 bg-purple-800 text-white font-medium rounded-md">
                <div className="animate-pulse w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                Video Recording Active
              </div>
            )}
          </div>
        )}
        <div className="flex space-x-2">
          <button
            onClick={onSimulateJSError}
            disabled={isRecording}
            className="flex items-center justify-center flex-1 px-3 py-2 bg-yellow-500 hover:bg-yellow-400 text-slate-900 font-medium rounded-md shadow-md transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-yellow-300 focus:ring-opacity-75 text-sm"
          >
            <ErrorIcon className="w-4 h-4 mr-1" />
            JS Error
          </button>
          <button
            onClick={onSimulateNetworkError}
            disabled={isRecording}
            className="flex items-center justify-center flex-1 px-3 py-2 bg-orange-500 hover:bg-orange-400 text-slate-900 font-medium rounded-md shadow-md transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-300 focus:ring-opacity-75 text-sm"
          >
            <ErrorIcon className="w-4 h-4 mr-1" />
            Network Error
          </button>
        </div>
      </div>
      {isRecording && (
        <div className="text-center text-xs text-green-400 mt-2 space-y-1">
          <p className="flex items-center justify-center">
            <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></span>
            Comprehensive recording in progress...
          </p>
          <p className="text-slate-400">
            Capturing: User interactions • Screen recording • Network requests • Console logs • DOM changes
          </p>
        </div>
      )}
    </div>
  );
};
    