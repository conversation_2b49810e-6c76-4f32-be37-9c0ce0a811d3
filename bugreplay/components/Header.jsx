
import React from 'react';
import { BugIcon } from './icons/BugIcon.jsx';

/**
 * Header component for the BugReplay extension
 * @param {Object} props - Component props
 * @param {string} props.currentView - Current active view
 * @param {Function} props.onViewChange - Function to handle view changes
 * @returns {JSX.Element} The header component
 */
export const Header = ({ currentView = 'recording', onViewChange }) => {
  return (
    <header className="w-full text-center p-4 border-b border-slate-700">
      <div className="flex items-center justify-center space-x-2 mb-1">
        <BugIcon className="w-8 h-8 text-indigo-400" />
        <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">
          BugReplay
        </h1>
      </div>
      <p className="text-slate-400 text-sm">"Catch Bugs the Right Way"</p>

      {/* Navigation Tabs */}
      {onViewChange && (
        <div className="mt-3 flex justify-center space-x-1">
          <button
            onClick={() => onViewChange('recording')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentView === 'recording'
                ? 'bg-indigo-600 text-white'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            Recording
          </button>
          <button
            onClick={() => onViewChange('sessions')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentView === 'sessions'
                ? 'bg-indigo-600 text-white'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            Sessions
          </button>
        </div>
      )}
    </header>
  );
};
    