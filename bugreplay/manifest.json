{"manifest_version": 3, "name": "BugReplay - <PERSON> Bugs the Right Way", "version": "1.0.1", "description": "A browser extension that records user actions, DOM changes, network logs, and console errors.", "permissions": ["storage", "activeTab", "scripting", "tabs", "debugger", "webRequest", "desktopCapture", "unlimitedStorage"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "index.html"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}], "web_accessible_resources": [{"resources": ["icons/*.png", "popup.jsx"], "matches": ["<all_urls>"]}]}