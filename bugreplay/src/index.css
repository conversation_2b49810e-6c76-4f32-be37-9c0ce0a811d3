@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the extension */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  width: 600px; /* Typical extension popup width */
  overflow-x: hidden; /* Prevent horizontal scrollbar for fixed width */
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d3748; /* slate-800 */
}

::-webkit-scrollbar-thumb {
  background: #4a5568; /* slate-600 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #718096; /* slate-500 */
}
