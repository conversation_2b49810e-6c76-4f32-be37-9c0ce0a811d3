
/**
 * Log types enum - represents different types of log entries
 * @readonly
 * @enum {string}
 */
export const LogType = {
  USER_ACTION: 'USER_ACTION',
  CONSOLE_LOG: 'CONSOLE_LOG',
  CONSOLE_ERROR: 'CONSOLE_ERROR',
  CONSOLE_WARN: 'CONSOLE_WARN',
  CONSOLE_INFO: 'CONSOLE_INFO',
  NETWORK_REQUEST: 'NETWORK_REQUEST',
  NETWORK_RESPONSE: 'NETWORK_RESPONSE',
  DOM_MUTATION: 'DOM_MUTATION',
  SCREENSHOT: 'SCREENSHOT',
  VIDEO_CHUNK: 'VIDEO_CHUNK',
  SYSTEM: 'SYSTEM', // For app-specific messages like recording started/stopped
};

/**
 * Recording state enum
 * @readonly
 * @enum {string}
 */
export const RecordingState = {
  IDLE: 'IDLE',
  STARTING: 'STARTING',
  RECORDING: 'RECORDING',
  STOPPING: 'STOPPING',
  PROCESSING: 'PROCESSING',
  ERROR: 'ERROR'
};

/**
 * @typedef {Object} LogEntry
 * @property {number} id - Unique identifier for the log entry
 * @property {Date} timestamp - When the log entry was created
 * @property {string} type - Type of log entry (should be one of LogType values)
 * @property {string} message - The log message content
 * @property {string} [status] - Optional status like 'success', 'error', 'warning' for network requests or console types
 */

/**
 * @typedef {Object} SimulatedError
 * @property {string} type - Type of the simulated error
 * @property {string} message - Error message
 * @property {string} screenshotUrl - URL or data URL of the screenshot
 */

/**
 * @typedef {Object} RecordingSession
 * @property {string} id - Unique session identifier
 * @property {Date} startTime - When recording started
 * @property {Date} endTime - When recording ended
 * @property {string} tabId - ID of the recorded tab
 * @property {string} url - URL of the recorded page
 * @property {string} title - Title of the recorded page
 * @property {LogEntry[]} logs - All captured log entries
 * @property {string[]} screenshots - Array of screenshot data URLs
 * @property {string} videoUrl - URL to the recorded video
 * @property {Object} harData - HAR (HTTP Archive) data
 * @property {RecordingState} state - Current recording state
 */

/**
 * @typedef {Object} UserInteraction
 * @property {string} type - Type of interaction (click, scroll, keypress, etc.)
 * @property {string} target - CSS selector or description of target element
 * @property {Object} coordinates - x, y coordinates if applicable
 * @property {string} value - Input value for form interactions
 * @property {Date} timestamp - When the interaction occurred
 */

/**
 * @typedef {Object} NetworkEntry
 * @property {string} url - Request URL
 * @property {string} method - HTTP method
 * @property {number} status - Response status code
 * @property {Object} requestHeaders - Request headers
 * @property {Object} responseHeaders - Response headers
 * @property {string} requestBody - Request payload
 * @property {string} responseBody - Response payload
 * @property {number} startTime - Request start time
 * @property {number} endTime - Request end time
 * @property {number} size - Response size in bytes
 */

/**
 * @typedef {Object} BugReport
 * @property {string} title - Title of the bug report
 * @property {string} stepsToReproduce - Steps to reproduce the bug
 * @property {string} expectedResult - What should have happened
 * @property {string} actualResult - What actually happened
 * @property {string} environment - Environment information
 * @property {string} logs - Stringified logs
 * @property {string} screenshotUrl - URL or data URL of the screenshot
 * @property {RecordingSession} recordingSession - Associated recording session data
 * @property {string[]} attachments - Array of attachment URLs/data
 */