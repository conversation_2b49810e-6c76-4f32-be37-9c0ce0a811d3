<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Recording State Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007cba;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #005a87;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.warning {
            background: #ffc107;
            color: #000;
        }
        .button.success {
            background: #28a745;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f0f0f0;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .logs {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #007cba;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🐛 BugReplay - Recording State Management Test</h1>
    
    <div class="info status">
        <strong>Test Purpose:</strong> Validate the recording state management fixes that prevent "Recording already in progress" errors and provide automatic recovery mechanisms.
    </div>

    <!-- State Testing -->
    <div class="test-section">
        <h2>📊 Recording State Testing</h2>
        <p>Test the enhanced recording state management and recovery mechanisms.</p>
        
        <div class="test-grid">
            <button class="button" onclick="checkRecordingState()">Check Current State</button>
            <button class="button" onclick="startRecording()">Start Recording</button>
            <button class="button" onclick="stopRecording()">Stop Recording</button>
            <button class="button warning" onclick="forceResetState()">Force Reset State</button>
        </div>
        
        <div id="state-status" class="status">
            Click buttons above to test recording state management...
        </div>
    </div>

    <!-- Multiple Start Attempts -->
    <div class="test-section">
        <h2>🔄 Multiple Start Attempts</h2>
        <p>Test how the system handles multiple recording start attempts.</p>
        
        <div class="test-grid">
            <button class="button" onclick="rapidStartAttempts()">Rapid Start Attempts</button>
            <button class="button" onclick="startWhileRecording()">Start While Recording</button>
            <button class="button" onclick="simulateStateInconsistency()">Simulate State Issue</button>
        </div>
        
        <div id="multiple-start-status" class="status">
            Test multiple start scenarios...
        </div>
    </div>

    <!-- Error Recovery -->
    <div class="test-section">
        <h2>🛠️ Error Recovery Testing</h2>
        <p>Test the automatic error recovery and force reset functionality.</p>
        
        <div class="test-grid">
            <button class="button danger" onclick="simulateRecordingError()">Simulate Recording Error</button>
            <button class="button warning" onclick="testForceReset()">Test Force Reset</button>
            <button class="button" onclick="testStateValidation()">Test State Validation</button>
        </div>
        
        <div id="recovery-status" class="status">
            Test error recovery mechanisms...
        </div>
    </div>

    <!-- Test Logs -->
    <div class="test-section">
        <h2>📋 Test Logs</h2>
        <div id="test-logs" class="logs">
            Test logs will appear here...
        </div>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        // Logging utility
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`BugReplay Test: ${logEntry.trim()}`);
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = 'Test logs cleared...\n';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        // State Testing Functions
        function checkRecordingState() {
            log('Checking current recording state...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_RECORDING_STATE' }, (response) => {
                    if (chrome.runtime.lastError) {
                        const error = chrome.runtime.lastError.message;
                        updateStatus('state-status', `Error: ${error}`, 'error');
                        log(`State check error: ${error}`, 'error');
                    } else {
                        const state = response.state || 'unknown';
                        const isRecording = response.isRecording || false;
                        const sessionId = response.sessionId || 'none';
                        
                        updateStatus('state-status', 
                            `State: ${state}, Recording: ${isRecording}, Session: ${sessionId}`, 
                            isRecording ? 'success' : 'info'
                        );
                        log(`Current state: ${state}, Recording: ${isRecording}, Session: ${sessionId}`, 'info');
                    }
                });
            } else {
                updateStatus('state-status', 'Extension not available', 'error');
                log('Chrome extension API not available', 'error');
            }
        }

        function startRecording() {
            log('Attempting to start recording...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'START_RECORDING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        const error = chrome.runtime.lastError.message;
                        updateStatus('state-status', `Start failed: ${error}`, 'error');
                        log(`Start recording error: ${error}`, 'error');
                    } else if (response && response.success) {
                        const message = response.alreadyRecording ? 
                            `Recording resumed: ${response.sessionId}` : 
                            `Recording started: ${response.sessionId}`;
                        updateStatus('state-status', message, 'success');
                        log(message, 'success');
                    } else {
                        const error = response?.error || 'Unknown error';
                        updateStatus('state-status', `Start failed: ${error}`, 'error');
                        log(`Start recording failed: ${error}`, 'error');
                    }
                });
            } else {
                updateStatus('state-status', 'Extension not available', 'error');
                log('Chrome extension API not available', 'error');
            }
        }

        function stopRecording() {
            log('Attempting to stop recording...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'STOP_RECORDING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        const error = chrome.runtime.lastError.message;
                        updateStatus('state-status', `Stop failed: ${error}`, 'error');
                        log(`Stop recording error: ${error}`, 'error');
                    } else if (response && response.success) {
                        updateStatus('state-status', 'Recording stopped successfully', 'success');
                        log('Recording stopped successfully', 'success');
                    } else {
                        const error = response?.error || 'Unknown error';
                        updateStatus('state-status', `Stop failed: ${error}`, 'error');
                        log(`Stop recording failed: ${error}`, 'error');
                    }
                });
            } else {
                updateStatus('state-status', 'Extension not available', 'error');
                log('Chrome extension API not available', 'error');
            }
        }

        function forceResetState() {
            log('Attempting force reset...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'FORCE_RESET_RECORDING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        const error = chrome.runtime.lastError.message;
                        updateStatus('state-status', `Force reset failed: ${error}`, 'error');
                        log(`Force reset error: ${error}`, 'error');
                    } else if (response && response.success) {
                        updateStatus('state-status', 'State reset successfully', 'success');
                        log('Recording state reset successfully', 'success');
                    } else {
                        const error = response?.error || 'Unknown error';
                        updateStatus('state-status', `Force reset failed: ${error}`, 'error');
                        log(`Force reset failed: ${error}`, 'error');
                    }
                });
            } else {
                updateStatus('state-status', 'Extension not available', 'error');
                log('Chrome extension API not available', 'error');
            }
        }

        // Multiple Start Attempts
        function rapidStartAttempts() {
            log('Testing rapid start attempts...');
            updateStatus('multiple-start-status', 'Sending multiple start requests...', 'warning');
            
            // Send 5 rapid start requests
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    chrome.runtime.sendMessage({ type: 'START_RECORDING' }, (response) => {
                        log(`Start attempt ${i + 1}: ${response?.success ? 'Success' : 'Failed'}`, 
                            response?.success ? 'success' : 'warning');
                    });
                }, i * 100);
            }
            
            setTimeout(() => {
                updateStatus('multiple-start-status', 'Rapid start attempts completed - check logs', 'info');
            }, 1000);
        }

        function startWhileRecording() {
            log('Testing start while recording...');
            
            // First start a recording
            chrome.runtime.sendMessage({ type: 'START_RECORDING' }, (response) => {
                if (response?.success) {
                    log('First recording started', 'success');
                    
                    // Then try to start another
                    setTimeout(() => {
                        chrome.runtime.sendMessage({ type: 'START_RECORDING' }, (response2) => {
                            if (response2?.success && response2?.alreadyRecording) {
                                updateStatus('multiple-start-status', 'Correctly handled already recording state', 'success');
                                log('Second start correctly returned existing session', 'success');
                            } else {
                                updateStatus('multiple-start-status', 'Unexpected response to second start', 'warning');
                                log('Unexpected response to second start attempt', 'warning');
                            }
                        });
                    }, 500);
                } else {
                    updateStatus('multiple-start-status', 'Failed to start initial recording', 'error');
                    log('Failed to start initial recording for test', 'error');
                }
            });
        }

        function simulateStateInconsistency() {
            log('Simulating state inconsistency...');
            updateStatus('multiple-start-status', 'This would require internal state manipulation', 'info');
            log('State inconsistency simulation requires internal access', 'info');
        }

        // Error Recovery Testing
        function simulateRecordingError() {
            log('Simulating recording error...');
            updateStatus('recovery-status', 'Error simulation would require internal access', 'info');
            log('Recording error simulation requires internal access', 'info');
        }

        function testForceReset() {
            log('Testing force reset functionality...');
            forceResetState();
            updateStatus('recovery-status', 'Force reset test completed - check state', 'info');
        }

        function testStateValidation() {
            log('Testing state validation...');
            checkRecordingState();
            updateStatus('recovery-status', 'State validation test completed - check logs', 'info');
        }

        // Initialize
        log('Recording state management test page loaded');
        log('Extension API available: ' + (typeof chrome !== 'undefined' && chrome.runtime ? 'Yes' : 'No'));
        
        // Auto-check state on load
        setTimeout(checkRecordingState, 1000);
    </script>
</body>
</html>
