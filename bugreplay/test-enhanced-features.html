<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay Enhanced Features Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h2 {
            color: #4a5568;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .primary-btn {
            background: #48bb78;
            font-weight: bold;
            font-size: 16px;
        }
        .primary-btn:hover {
            background: #38a169;
        }
        .danger-btn {
            background: #f56565;
        }
        .danger-btn:hover {
            background: #e53e3e;
        }
        .warning-btn {
            background: #ed8936;
        }
        .warning-btn:hover {
            background: #dd6b20;
        }
        input, textarea, select {
            padding: 10px;
            margin: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #667eea;
            outline: none;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4a5568;
        }
        .nav-menu {
            background: #2d3748;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        .nav-menu a:hover {
            background: #4a5568;
        }
        .sidebar {
            background: #edf2f7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            background: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
        }
        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }
        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .test-instructions {
            background: #bee3f8;
            border: 2px solid #3182ce;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .test-instructions h1 {
            color: #2c5282;
            margin-top: 0;
        }
        .feature-highlight {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            padding: 15px;
            margin: 15px 0;
        }
        .log-display {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-instructions">
        <h1>🎯 BugReplay Enhanced Features Test Page</h1>
        <p><strong>Test the following enhancements:</strong></p>
        <ol>
            <li><strong>Enhanced User Action Descriptions:</strong> Click various elements to see descriptive logging</li>
            <li><strong>Session Import/Export:</strong> Use the Sessions tab to export and import session data</li>
            <li><strong>Video Recording:</strong> Start recording to capture screen video with local file saving</li>
            <li><strong>Manual Screenshots:</strong> Use the "Take Screenshot" button during recording</li>
        </ol>
        <div class="feature-highlight">
            <strong>🚀 New Features:</strong> More descriptive logs, session import, working video recording, and manual screenshots!
        </div>
    </div>

    <div class="container">
        <!-- Navigation Menu for Testing Context -->
        <nav class="nav-menu" role="navigation" aria-label="Main navigation">
            <a href="#home" id="home-link">🏠 Home</a>
            <a href="#products" id="products-link">📦 Products</a>
            <a href="#services" id="services-link">⚙️ Services</a>
            <div class="dropdown">
                <a href="#settings" id="settings-dropdown">⚙️ Settings ▼</a>
                <div class="dropdown-content">
                    <a href="#profile" id="profile-link">👤 Profile</a>
                    <a href="#preferences" id="preferences-link">🎛️ Preferences</a>
                    <a href="#logout" id="logout-link">🚪 Logout</a>
                </div>
            </div>
        </nav>

        <!-- Section 1: Button Interactions -->
        <div class="section">
            <h2>🔘 Button Interactions (Enhanced Descriptions)</h2>
            <p>Click these buttons to test enhanced user action logging with descriptive context:</p>
            
            <button class="primary-btn" id="save-btn" aria-label="Save current document">💾 Save Document</button>
            <button class="danger-btn" id="delete-btn" title="Delete selected items">🗑️ Delete Selected</button>
            <button class="warning-btn" id="export-btn">📤 Export Data</button>
            <button id="generic-btn">Generic Button</button>
            
            <div style="margin-top: 20px;">
                <button id="modal-trigger">🪟 Open Modal Dialog</button>
                <button id="confirm-action" onclick="confirmAction()">✅ Confirm Action</button>
            </div>
        </div>

        <!-- Section 2: Form Interactions -->
        <div class="section">
            <h2>📝 Form Interactions (Enhanced Field Descriptions)</h2>
            <p>Interact with these form elements to test enhanced input field logging:</p>
            
            <form id="test-form" name="user-registration">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" placeholder="Enter your username" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" placeholder="Enter secure password">
                </div>
                
                <div class="form-group">
                    <label for="bio">Biography:</label>
                    <textarea id="bio" name="bio" placeholder="Tell us about yourself..." rows="4"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="country">Country:</label>
                    <select id="country" name="country">
                        <option value="">Select your country</option>
                        <option value="us">United States</option>
                        <option value="ca">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="de">Germany</option>
                    </select>
                </div>
                
                <button type="submit" class="primary-btn">🚀 Register Account</button>
                <button type="reset" class="warning-btn">🔄 Reset Form</button>
            </form>
        </div>

        <!-- Section 3: Link Interactions -->
        <div class="section">
            <h2>🔗 Link Interactions (Enhanced Link Descriptions)</h2>
            <p>Click these links to test enhanced link action logging:</p>
            
            <div style="margin: 15px 0;">
                <a href="#documentation" id="docs-link" title="View complete documentation">📚 Documentation</a> |
                <a href="https://example.com" id="external-link" target="_blank">🌐 External Website</a> |
                <a href="#contact" id="contact-link" aria-label="Contact support team">📞 Contact Support</a>
            </div>
            
            <div class="sidebar">
                <h3>Sidebar Navigation</h3>
                <a href="#recent" id="recent-link">📋 Recent Items</a><br>
                <a href="#favorites" id="favorites-link">⭐ Favorites</a><br>
                <a href="#archive" id="archive-link">📦 Archive</a>
            </div>
        </div>

        <!-- Section 4: Interactive Elements -->
        <div class="section">
            <h2>🎮 Interactive Elements (Context Testing)</h2>
            <p>Test various interactive elements for enhanced context capture:</p>
            
            <div style="margin: 15px 0;">
                <input type="checkbox" id="terms" name="terms">
                <label for="terms">I agree to the Terms and Conditions</label>
            </div>
            
            <div style="margin: 15px 0;">
                <input type="radio" id="option1" name="choice" value="1">
                <label for="option1">Option 1</label>
                <input type="radio" id="option2" name="choice" value="2">
                <label for="option2">Option 2</label>
            </div>
            
            <div style="margin: 15px 0;">
                <input type="range" id="volume" name="volume" min="0" max="100" value="50">
                <label for="volume">Volume Control</label>
            </div>
            
            <div style="margin: 15px 0;">
                <input type="file" id="file-upload" name="file" accept=".pdf,.doc,.docx">
                <label for="file-upload">Upload Document</label>
            </div>
        </div>

        <!-- Log Display -->
        <div class="section">
            <h2>📊 Action Log Display</h2>
            <p>Watch the enhanced action descriptions appear here:</p>
            <div class="log-display" id="action-log">
                <div>🎯 Enhanced BugReplay logging initialized...</div>
                <div>📝 Click, type, or interact with elements above to see descriptive logs</div>
            </div>
        </div>
    </div>

    <!-- Modal Dialog -->
    <div id="test-modal" class="modal">
        <div class="modal-content">
            <span class="close" id="modal-close">&times;</span>
            <h2>Test Modal Dialog</h2>
            <p>This is a modal dialog for testing enhanced context capture.</p>
            <button id="modal-ok" class="primary-btn">OK</button>
            <button id="modal-cancel">Cancel</button>
        </div>
    </div>

    <script>
        // Enhanced logging for demonstration
        let logCount = 0;
        
        function addToLog(message) {
            const logDisplay = document.getElementById('action-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #68d391;">[${timestamp}]</span> ${message}`;
            logDisplay.appendChild(logEntry);
            logDisplay.scrollTop = logDisplay.scrollHeight;
            
            // Keep only last 20 entries
            while (logDisplay.children.length > 20) {
                logDisplay.removeChild(logDisplay.firstChild);
            }
        }

        // Modal functionality
        const modal = document.getElementById('test-modal');
        const modalTrigger = document.getElementById('modal-trigger');
        const modalClose = document.getElementById('modal-close');
        
        modalTrigger.onclick = function() {
            modal.style.display = 'block';
            addToLog('🪟 Modal dialog opened');
        }
        
        modalClose.onclick = function() {
            modal.style.display = 'none';
            addToLog('❌ Modal dialog closed');
        }
        
        document.getElementById('modal-ok').onclick = function() {
            modal.style.display = 'none';
            addToLog('✅ Modal confirmed and closed');
        }
        
        document.getElementById('modal-cancel').onclick = function() {
            modal.style.display = 'none';
            addToLog('🚫 Modal cancelled and closed');
        }

        // Form submission handler
        document.getElementById('test-form').onsubmit = function(e) {
            e.preventDefault();
            addToLog('📋 Form submission intercepted (prevented for demo)');
        }

        // Confirm action function
        function confirmAction() {
            if (confirm('Are you sure you want to perform this action?')) {
                addToLog('✅ User confirmed the action');
            } else {
                addToLog('❌ User cancelled the action');
            }
        }

        // Add event listeners for demonstration
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A') {
                const text = e.target.textContent || e.target.innerText;
                addToLog(`🖱️ Clicked: ${text.trim()}`);
            }
        });

        document.addEventListener('input', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                const label = e.target.labels?.[0]?.textContent || e.target.placeholder || e.target.name || 'input field';
                addToLog(`⌨️ Typing in: ${label}`);
            }
        });

        // Scroll tracking
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
                addToLog(`📜 Scrolled to ${scrollPercent}% of page`);
            }, 150);
        });

        // Initialize
        addToLog('🚀 Test page loaded and ready for enhanced BugReplay testing!');
    </script>
</body>
</html>
