/* global chrome */

// content.js
let isCapturing = false;
let currentSessionId = null;
const originalConsole = {};
const originalFetch = window.fetch;
let mutationObserver = null;
let interactionListeners = [];
let lastScrollTime = 0;
let mediaRecorder = null;
let recordedChunks = [];

/**
 * Send log data to the extension popup and background script
 * @param {Object} logData - The log data to send
 */
function sendLog(logData) {
  const logEntry = {
    ...logData,
    timestamp: new Date().toISOString(), // Ensure timestamp is fresh
  };

  try {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      // Send to popup for real-time display
      chrome.runtime.sendMessage({
        type: 'LOG_ENTRY_FROM_CONTENT',
        payload: logEntry,
      });

      // Send to background script for persistence
      chrome.runtime.sendMessage({
        type: 'ADD_LOG_ENTRY',
        logEntry: logEntry,
      });
    } else {
      // console.warn('BugReplay: chrome.runtime.sendMessage not available to send log.');
    }
  } catch (error) {
    // console.warn('BugReplay: Could not send log, popup might be closed.', error);
    // This can happen if the popup is not open.
    // Logs are still sent to background script for persistence.
  }
}

/**
 * Override console methods to capture logs
 */
function overrideConsole() {
  ['log', 'warn', 'error', 'info', 'debug'].forEach((method) => {
    if (console[method]) {
      originalConsole[method] = console[method];
      console[method] = (...args) => {
        originalConsole[method](...args);
        if (isCapturing) {
          const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
          sendLog({
            type: method === 'error' ? 'CONSOLE_ERROR' : 'CONSOLE_LOG',
            message: `[Console.${method}] ${message}`,
            status: method === 'warn' ? 'warning' : undefined,
          });
        }
      };
    }
  });
}

/**
 * Restore original console methods
 */
function restoreConsole() {
  Object.keys(originalConsole).forEach((method) => {
    if (originalConsole[method]) {
      console[method] = originalConsole[method];
    }
  });
}

/**
 * Override fetch to capture network requests
 */
function overrideFetch() {
    window.fetch = async (...args) => {
        const [resource, config] = args;
        const requestUrl = resource instanceof Request ? resource.url : String(resource);
        const requestMethod = config?.method?.toUpperCase() || (resource instanceof Request ? resource.method.toUpperCase() : 'GET');

        if (isCapturing) {
            sendLog({
                type: 'NETWORK_REQUEST',
                message: `${requestMethod} ${requestUrl} - Initiated`,
                status: 'pending'
            });
        }

        try {
            const response = await originalFetch(...args);
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - ${response.status} ${response.statusText}`,
                    status: response.ok ? 'success' : 'error',
                });
            }
            return response;
        } catch (error) {
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - Failed: ${error.message}`,
                    status: 'error',
                });
            }
            throw error;
        }
    };
}

/**
 * Restore original fetch function
 */
function restoreFetch() {
    window.fetch = originalFetch;
}

/**
 * Get meaningful element description for user actions
 * @param {Element} element - DOM element
 * @returns {string} Human-readable description
 */
function getElementDescription(element) {
  const tagName = element.tagName.toLowerCase();

  // Check for button text or value
  if (tagName === 'button' || (tagName === 'input' && element.type === 'button') || (tagName === 'input' && element.type === 'submit')) {
    const buttonText = element.textContent?.trim() || element.value || element.getAttribute('aria-label');
    if (buttonText) {
      return `'${buttonText}' button`;
    }
  }

  // Check for link text
  if (tagName === 'a') {
    const linkText = element.textContent?.trim() || element.getAttribute('aria-label') || element.getAttribute('title');
    if (linkText) {
      return `'${linkText}' link`;
    }
    const href = element.getAttribute('href');
    if (href) {
      return `link to '${href}'`;
    }
  }

  // Check for form inputs with labels
  if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
    const inputType = element.type || 'text';
    const label = getInputLabel(element);
    const placeholder = element.getAttribute('placeholder');

    if (label) {
      return `'${label}' ${inputType} field`;
    } else if (placeholder) {
      return `${inputType} field with placeholder '${placeholder}'`;
    } else {
      return `${inputType} field`;
    }
  }

  // Check for images with alt text
  if (tagName === 'img') {
    const alt = element.getAttribute('alt') || element.getAttribute('title');
    if (alt) {
      return `image '${alt}'`;
    }
    return 'image';
  }

  // Check for elements with aria-label
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    return `'${ariaLabel}' ${tagName}`;
  }

  // Check for elements with title
  const title = element.getAttribute('title');
  if (title) {
    return `${tagName} titled '${title}'`;
  }

  // Check for text content
  const text = element.textContent?.trim();
  if (text && text.length <= 50) {
    return `${tagName} containing '${text}'`;
  } else if (text && text.length > 50) {
    return `${tagName} containing '${text.substring(0, 47)}...'`;
  }

  // Check for common semantic elements
  if (tagName === 'nav') return 'navigation menu';
  if (tagName === 'header') return 'page header';
  if (tagName === 'footer') return 'page footer';
  if (tagName === 'main') return 'main content area';
  if (tagName === 'aside') return 'sidebar';
  if (tagName === 'article') return 'article';
  if (tagName === 'section') return 'section';

  // Fallback to basic element description
  const id = element.id;
  const className = element.className;

  if (id) {
    return `${tagName} with id '${id}'`;
  } else if (className) {
    const classes = className.split(' ').filter(c => c.trim()).slice(0, 2).join(' ');
    return `${tagName} with class '${classes}'`;
  }

  return tagName;
}

/**
 * Find label for form input element
 * @param {Element} element - Input element
 * @returns {string|null} Label text
 */
function getInputLabel(element) {
  // Check for explicit label association
  const id = element.id;
  if (id) {
    const label = document.querySelector(`label[for="${id}"]`);
    if (label) {
      return label.textContent?.trim();
    }
  }

  // Check for parent label
  const parentLabel = element.closest('label');
  if (parentLabel) {
    return parentLabel.textContent?.trim();
  }

  // Check for aria-labelledby
  const labelledBy = element.getAttribute('aria-labelledby');
  if (labelledBy) {
    const labelElement = document.getElementById(labelledBy);
    if (labelElement) {
      return labelElement.textContent?.trim();
    }
  }

  // Check for nearby text (common pattern)
  const previousSibling = element.previousElementSibling;
  if (previousSibling && (previousSibling.tagName.toLowerCase() === 'label' || previousSibling.textContent?.trim())) {
    return previousSibling.textContent?.trim();
  }

  return null;
}

/**
 * Get contextual location description
 * @param {Element} element - DOM element
 * @returns {string} Location context
 */
function getLocationContext(element) {
  // Check if element is in navigation
  const nav = element.closest('nav');
  if (nav) {
    const navLabel = nav.getAttribute('aria-label') || nav.getAttribute('role');
    return navLabel ? `in ${navLabel} navigation` : 'in navigation menu';
  }

  // Check if element is in header
  const header = element.closest('header');
  if (header) {
    return 'in page header';
  }

  // Check if element is in footer
  const footer = element.closest('footer');
  if (footer) {
    return 'in page footer';
  }

  // Check if element is in sidebar
  const aside = element.closest('aside');
  if (aside) {
    return 'in sidebar';
  }

  // Check if element is in modal or dialog
  const modal = element.closest('[role="dialog"], .modal, .popup');
  if (modal) {
    return 'in modal dialog';
  }

  // Check if element is in form
  const form = element.closest('form');
  if (form) {
    const formName = form.getAttribute('name') || form.getAttribute('id');
    return formName ? `in '${formName}' form` : 'in form';
  }

  return '';
}

/**
 * Handle user click actions
 * @param {Event} event - Click event
 */
function handleUserAction(event) {
  if (!isCapturing || !event.target) return;

  const target = event.target;
  const elementDescription = getElementDescription(target);
  const locationContext = getLocationContext(target);

  let description = `User clicked on ${elementDescription}`;
  if (locationContext) {
    description += ` ${locationContext}`;
  }

  const interaction = {
    type: 'click',
    target: generateSelector(target),
    coordinates: { x: event.clientX, y: event.clientY },
    timestamp: new Date(),
    description: description,
    elementText: target.textContent?.trim().substring(0, 100) || '',
    elementType: target.tagName.toLowerCase(),
    elementId: target.id || '',
    elementClasses: target.className || ''
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Handle scroll events
 * @param {Event} event - Scroll event
 */
function handleScrollAction(event) {
  if (!isCapturing) return;

  const now = Date.now();
  if (now - lastScrollTime < 100) return; // Throttle scroll events
  lastScrollTime = now;

  const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
  const scrollY = window.pageYOffset || document.documentElement.scrollTop;

  // Calculate scroll direction and distance
  const scrollDirection = getScrollDirection(scrollY);
  const scrollPercentage = getScrollPercentage();

  let description = `User scrolled ${scrollDirection}`;
  if (scrollPercentage !== null) {
    description += ` to ${scrollPercentage}% of page`;
  }
  description += ` (position: ${scrollX}, ${scrollY})`;

  const interaction = {
    type: 'scroll',
    target: 'window',
    coordinates: { x: scrollX, y: scrollY },
    timestamp: new Date(),
    description: description,
    scrollDirection: scrollDirection,
    scrollPercentage: scrollPercentage
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Determine scroll direction
 * @param {number} currentScrollY - Current scroll position
 * @returns {string} Scroll direction
 */
function getScrollDirection(currentScrollY) {
  const lastScrollY = window.lastScrollY || 0;
  window.lastScrollY = currentScrollY;

  if (currentScrollY > lastScrollY) {
    return 'down';
  } else if (currentScrollY < lastScrollY) {
    return 'up';
  }
  return 'horizontally';
}

/**
 * Calculate scroll percentage
 * @returns {number|null} Scroll percentage
 */
function getScrollPercentage() {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;

  if (scrollHeight <= 0) return null;

  return Math.round((scrollTop / scrollHeight) * 100);
}

/**
 * Handle keyboard input
 * @param {Event} event - Keyboard event
 */
function handleKeyboardAction(event) {
  if (!isCapturing) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();
  const type = target.type || '';

  if (tagName === 'input' || tagName === 'textarea') {
    const fieldDescription = getElementDescription(target);
    const locationContext = getLocationContext(target);

    let description;
    if (event.key === 'Enter') {
      description = `User pressed Enter in ${fieldDescription}`;
    } else if (event.key === 'Tab') {
      description = `User pressed Tab to navigate from ${fieldDescription}`;
    } else if (event.key === 'Escape') {
      description = `User pressed Escape in ${fieldDescription}`;
    } else if (event.key.length === 1) {
      description = `User typed "${event.key}" in ${fieldDescription}`;
    } else {
      description = `User pressed ${event.key} in ${fieldDescription}`;
    }

    if (locationContext) {
      description += ` ${locationContext}`;
    }

    const interaction = {
      type: 'keypress',
      target: generateSelector(target),
      key: event.key,
      value: target.value,
      timestamp: new Date(),
      description: description,
      fieldType: type,
      fieldLabel: getInputLabel(target) || '',
      isSpecialKey: event.key.length > 1
    };

    sendLog({
      type: 'USER_ACTION',
      message: description,
      interaction: interaction
    });
  }
}

/**
 * Handle form submissions
 * @param {Event} event - Submit event
 */
function handleFormSubmission(event) {
  if (!isCapturing) return;

  const form = event.target;
  const action = form.action || window.location.href;
  const method = form.method || 'GET';

  // Get form description
  const formName = form.getAttribute('name') || form.getAttribute('id');
  const formTitle = form.getAttribute('title') || form.getAttribute('aria-label');

  let formDescription = 'form';
  if (formTitle) {
    formDescription = `'${formTitle}' form`;
  } else if (formName) {
    formDescription = `'${formName}' form`;
  }

  // Get form context
  const locationContext = getLocationContext(form);

  // Count form fields
  const inputs = form.querySelectorAll('input, textarea, select');
  const fieldCount = inputs.length;

  let description = `User submitted ${formDescription}`;
  if (fieldCount > 0) {
    description += ` with ${fieldCount} field${fieldCount !== 1 ? 's' : ''}`;
  }
  if (locationContext) {
    description += ` ${locationContext}`;
  }
  description += ` (${method.toUpperCase()} to ${action})`;

  const interaction = {
    type: 'submit',
    target: generateSelector(form),
    action: action,
    method: method,
    timestamp: new Date(),
    description: description,
    formName: formName || '',
    formTitle: formTitle || '',
    fieldCount: fieldCount
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Generate CSS selector for an element with semantic information
 * @param {Element} element - DOM element
 * @returns {string} CSS selector
 */
function generateSelector(element) {
  // Priority 1: Use ID if available
  if (element.id) {
    return `#${element.id}`;
  }

  // Priority 2: Use semantic attributes for better selectors
  const tagName = element.tagName.toLowerCase();

  // For buttons, try to use text content or value
  if (tagName === 'button' || (tagName === 'input' && ['button', 'submit'].includes(element.type))) {
    const buttonText = element.textContent?.trim() || element.value;
    if (buttonText) {
      // Create a selector that includes text for better identification
      const textSelector = buttonText.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      if (textSelector) {
        return `${tagName}[data-text*="${textSelector}"], ${tagName}:contains("${buttonText}")`;
      }
    }
  }

  // For links, try to use href or text
  if (tagName === 'a') {
    const href = element.getAttribute('href');
    const linkText = element.textContent?.trim();
    if (href) {
      return `a[href="${href}"]`;
    } else if (linkText) {
      const textSelector = linkText.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      if (textSelector) {
        return `a:contains("${linkText}")`;
      }
    }
  }

  // For form inputs, try to use name, type, or associated label
  if (['input', 'textarea', 'select'].includes(tagName)) {
    const name = element.getAttribute('name');
    const type = element.getAttribute('type');
    const placeholder = element.getAttribute('placeholder');

    if (name) {
      return `${tagName}[name="${name}"]`;
    } else if (type && tagName === 'input') {
      return `input[type="${type}"]`;
    } else if (placeholder) {
      return `${tagName}[placeholder="${placeholder}"]`;
    }
  }

  // Priority 3: Use meaningful class names
  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    // Prioritize semantic class names
    const semanticClasses = classes.filter(c =>
      c.includes('btn') || c.includes('button') || c.includes('link') ||
      c.includes('nav') || c.includes('menu') || c.includes('form') ||
      c.includes('input') || c.includes('field') || c.includes('submit')
    );

    if (semanticClasses.length > 0) {
      return `${tagName}.${semanticClasses.slice(0, 2).join('.')}`;
    } else if (classes.length > 0) {
      return `${tagName}.${classes.slice(0, 2).join('.')}`;
    }
  }

  // Priority 4: Use aria-label or role
  const ariaLabel = element.getAttribute('aria-label');
  const role = element.getAttribute('role');
  if (ariaLabel) {
    return `${tagName}[aria-label="${ariaLabel}"]`;
  } else if (role) {
    return `${tagName}[role="${role}"]`;
  }

  // Priority 5: Generate path-based selector with semantic context
  const path = [];
  let current = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let selector = current.tagName.toLowerCase();

    // Add semantic context to path elements
    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break;
    }

    // Add meaningful attributes to path
    const role = current.getAttribute('role');
    const ariaLabel = current.getAttribute('aria-label');
    if (role) {
      selector += `[role="${role}"]`;
    } else if (ariaLabel) {
      selector += `[aria-label="${ariaLabel}"]`;
    } else if (current.className) {
      const classes = current.className.split(' ').filter(c => c.trim());
      const semanticClass = classes.find(c =>
        c.includes('nav') || c.includes('menu') || c.includes('header') ||
        c.includes('footer') || c.includes('main') || c.includes('content')
      );
      if (semanticClass) {
        selector += `.${semanticClass}`;
      } else if (classes.length > 0) {
        selector += `.${classes[0]}`;
      }
    }

    // Add nth-child only if necessary
    const siblings = Array.from(current.parentNode?.children || []);
    const sameTagSiblings = siblings.filter(s => s.tagName === current.tagName);
    if (sameTagSiblings.length > 1) {
      const index = sameTagSiblings.indexOf(current);
      selector += `:nth-of-type(${index + 1})`;
    }

    path.unshift(selector);
    current = current.parentNode;

    if (path.length > 4) break; // Limit depth for readability
  }

  return path.join(' > ');
}

/**
 * Initialize DOM mutation observer
 */
function initializeMutationObserver() {
  mutationObserver = new MutationObserver((mutations) => {
    if (!isCapturing) return;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        const addedElements = Array.from(mutation.addedNodes)
          .filter(node => node.nodeType === Node.ELEMENT_NODE)
          .map(node => node.tagName?.toLowerCase())
          .filter(Boolean);

        if (addedElements.length > 0) {
          sendLog({
            type: 'DOM_MUTATION',
            message: `DOM elements added: ${addedElements.join(', ')}`,
            timestamp: new Date()
          });
        }
      }

      if (mutation.type === 'attributes') {
        sendLog({
          type: 'DOM_MUTATION',
          message: `Attribute '${mutation.attributeName}' changed on ${mutation.target.tagName?.toLowerCase()}`,
          timestamp: new Date()
        });
      }
    });
  });

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'style', 'data-*']
  });
}

/**
 * Setup all interaction listeners
 */
function setupInteractionListeners() {
  const listeners = [
    { event: 'click', handler: handleUserAction, options: true },
    { event: 'scroll', handler: handleScrollAction, options: false },
    { event: 'keydown', handler: handleKeyboardAction, options: false },
    { event: 'submit', handler: handleFormSubmission, options: false }
  ];

  listeners.forEach(({ event, handler, options }) => {
    document.addEventListener(event, handler, options);
    interactionListeners.push({ event, handler, options });
  });
}

/**
 * Remove all interaction listeners
 */
function removeInteractionListeners() {
  interactionListeners.forEach(({ event, handler, options }) => {
    document.removeEventListener(event, handler, options);
  });
  interactionListeners = [];
}

/**
 * Check if video recording is supported in current environment
 */
function isVideoRecordingSupported() {
  // Check if we're in a secure context (HTTPS or localhost)
  if (!window.isSecureContext) {
    return { supported: false, reason: 'Video recording requires HTTPS or localhost' };
  }

  // Check if getDisplayMedia is supported
  if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
    return { supported: false, reason: 'Screen capture not supported in this browser' };
  }

  // Check if MediaRecorder is supported
  if (!window.MediaRecorder) {
    return { supported: false, reason: 'MediaRecorder not supported in this browser' };
  }

  return { supported: true };
}

/**
 * Start video recording using getDisplayMedia API
 */
async function startVideoRecording() {
  try {
    // Check if video recording is supported
    const supportCheck = isVideoRecordingSupported();
    if (!supportCheck.supported) {
      throw new Error(supportCheck.reason);
    }

    // Show user guidance message
    sendLog({
      type: 'SYSTEM',
      message: 'Starting video recording - please select screen/window to share when prompted'
    });

    // Request screen capture permission with timeout
    const streamPromise = navigator.mediaDevices.getDisplayMedia({
      video: {
        mediaSource: 'screen',
        width: { ideal: 1920, max: 1920 },
        height: { ideal: 1080, max: 1080 },
        frameRate: { ideal: 30, max: 60 }
      },
      audio: false // We'll focus on video for now
    });

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Screen sharing request timed out')), 30000);
    });

    const stream = await Promise.race([streamPromise, timeoutPromise]);

    // Check if user cancelled the screen share
    if (!stream) {
      throw new Error('User cancelled screen sharing');
    }

    // Initialize MediaRecorder with optimal settings
    const options = {
      mimeType: 'video/webm;codecs=vp9,opus',
      videoBitsPerSecond: 2500000 // 2.5 Mbps for good quality
    };

    // Fallback to vp8 if vp9 is not supported
    if (!MediaRecorder.isTypeSupported(options.mimeType)) {
      options.mimeType = 'video/webm;codecs=vp8,opus';
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options.mimeType = 'video/webm';
      }
    }

    mediaRecorder = new MediaRecorder(stream, options);
    recordedChunks = [];

    // Handle data availability
    mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        recordedChunks.push(event.data);
        console.log('BugReplay: Video chunk recorded, size:', event.data.size);
      }
    };

    // Handle recording stop
    mediaRecorder.onstop = () => {
      console.log('BugReplay: MediaRecorder stopped, processing video...');
      processVideoRecording();
    };

    // Handle errors
    mediaRecorder.onerror = (event) => {
      console.error('BugReplay: MediaRecorder error:', event.error);
      sendLog({
        type: 'ERROR',
        message: `Video recording error: ${event.error.message}`
      });
    };

    // Handle stream end (user stops sharing)
    stream.getVideoTracks()[0].addEventListener('ended', () => {
      console.log('BugReplay: Screen sharing ended by user');
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }
      sendLog({
        type: 'SYSTEM',
        message: 'Screen sharing ended by user'
      });
    });

    // Start recording
    mediaRecorder.start(1000); // Collect data every second

    sendLog({
      type: 'SYSTEM',
      message: 'Video recording started - screen capture active'
    });

    console.log('BugReplay: Video recording started successfully');
    return { success: true, message: 'Video recording started' };

  } catch (error) {
    console.error('BugReplay: Failed to start video recording:', error);

    let errorMessage = error.message;
    let userGuidance = '';

    if (error.name === 'NotAllowedError') {
      errorMessage = 'Screen capture permission denied by user';
      userGuidance = 'Please allow screen sharing when prompted to enable video recording.';
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Screen capture not supported in this browser';
      userGuidance = 'Try using Chrome, Firefox, or Edge for video recording support.';
    } else if (error.name === 'AbortError') {
      errorMessage = 'Screen capture was cancelled by user';
      userGuidance = 'Click "Start Video" again and select a screen/window to share.';
    } else if (error.message.includes('HTTPS')) {
      userGuidance = 'Video recording requires HTTPS. Try accessing the site with https:// or use localhost.';
    } else if (error.message.includes('timed out')) {
      userGuidance = 'Screen sharing request timed out. Please try again and respond to the permission prompt quickly.';
    }

    const fullMessage = userGuidance ? `${errorMessage}. ${userGuidance}` : errorMessage;

    sendLog({
      type: 'ERROR',
      message: `Failed to start video recording: ${fullMessage}`
    });

    return { success: false, error: errorMessage, guidance: userGuidance };
  }
}

/**
 * Stop video recording
 */
async function stopVideoRecording() {
  try {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      console.log('BugReplay: Stopping video recording...');

      // Stop the MediaRecorder
      mediaRecorder.stop();

      // Stop all tracks to release screen capture
      if (mediaRecorder.stream) {
        mediaRecorder.stream.getTracks().forEach(track => {
          track.stop();
          console.log('BugReplay: Stopped video track:', track.kind);
        });
      }

      sendLog({
        type: 'SYSTEM',
        message: 'Video recording stopped and processing...'
      });

      return { success: true, message: 'Video recording stopped' };
    } else {
      console.warn('BugReplay: No active video recording to stop');
      return { success: false, error: 'No active recording' };
    }
  } catch (error) {
    console.error('BugReplay: Error stopping video recording:', error);
    sendLog({
      type: 'ERROR',
      message: `Error stopping video recording: ${error.message}`
    });
    return { success: false, error: error.message };
  }
}

/**
 * Process recorded video data
 */
async function processVideoRecording() {
  try {
    if (recordedChunks.length === 0) {
      console.warn('BugReplay: No video data recorded');
      sendLog({
        type: 'SYSTEM',
        message: 'No video data was recorded'
      });
      return;
    }

    console.log('BugReplay: Processing video data, chunks:', recordedChunks.length);

    // Determine the correct MIME type based on what was actually recorded
    let mimeType = 'video/webm';
    if (mediaRecorder && mediaRecorder.mimeType) {
      mimeType = mediaRecorder.mimeType;
    }

    // Create blob from recorded chunks
    const videoBlob = new Blob(recordedChunks, { type: mimeType });

    console.log('BugReplay: Video blob created, size:', videoBlob.size, 'type:', mimeType);

    if (videoBlob.size === 0) {
      throw new Error('Video blob is empty');
    }

    // Create object URL for the video
    const videoUrl = URL.createObjectURL(videoBlob);

    // Save video file locally
    await saveVideoFile(videoBlob, mimeType);

    // Send video data to background script
    chrome.runtime.sendMessage({
      type: 'VIDEO_RECORDING_DATA',
      videoData: {
        videoUrl: videoUrl,
        videoSize: videoBlob.size,
        mimeType: mimeType,
        duration: calculateVideoDuration(),
        recordedAt: new Date().toISOString()
      }
    });

    sendLog({
      type: 'SYSTEM',
      message: `Video recording saved successfully (${formatFileSize(videoBlob.size)})`
    });

    console.log('BugReplay: Video processing completed successfully');

  } catch (error) {
    console.error('BugReplay: Error processing video recording:', error);
    sendLog({
      type: 'ERROR',
      message: `Error processing video recording: ${error.message}`
    });
  }
}

/**
 * Calculate approximate video duration
 */
function calculateVideoDuration() {
  // This is an approximation based on recording chunks
  // Each chunk represents roughly 1 second of recording
  return recordedChunks.length;
}

/**
 * Save video file to local storage (downloads)
 */
async function saveVideoFile(videoBlob, mimeType = 'video/webm') {
  try {
    // Determine file extension based on MIME type
    let extension = 'webm';
    if (mimeType.includes('mp4')) {
      extension = 'mp4';
    } else if (mimeType.includes('webm')) {
      extension = 'webm';
    }

    // Generate filename with timestamp and session ID
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const sessionId = currentSessionId || 'unknown';
    const fileName = `bugreplay-video-${sessionId}-${timestamp}.${extension}`;

    console.log('BugReplay: Saving video file:', fileName, 'Size:', videoBlob.size);

    // Create download link
    const url = URL.createObjectURL(videoBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.style.display = 'none';

    // Add to DOM, trigger download, then remove
    document.body.appendChild(link);

    // Use a small delay to ensure the link is properly added to DOM
    await new Promise(resolve => setTimeout(resolve, 100));

    link.click();

    // Clean up after a delay to ensure download starts
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('BugReplay: Video download cleanup completed');
    }, 1000);

    sendLog({
      type: 'SYSTEM',
      message: `Video saved as ${fileName} (${formatFileSize(videoBlob.size)})`
    });

    console.log('BugReplay: Video file download initiated successfully');

  } catch (error) {
    console.error('BugReplay: Error saving video file:', error);
    sendLog({
      type: 'ERROR',
      message: `Error saving video file: ${error.message}`
    });
  }
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Start capturing user actions and logs
 */
function startCapturing() {
  if (isCapturing) return;
  isCapturing = true;
  currentSessionId = arguments[0]?.sessionId || null;

  overrideConsole();
  overrideFetch();
  setupInteractionListeners();
  initializeMutationObserver();

  sendLog({ type: 'SYSTEM', message: 'Content script started comprehensive capturing.' });
}

/**
 * Stop capturing user actions and logs
 */
function stopCapturing() {
  if (!isCapturing) return;
  isCapturing = false;
  currentSessionId = null;

  restoreConsole();
  restoreFetch();
  removeInteractionListeners();

  if (mutationObserver) {
    mutationObserver.disconnect();
    mutationObserver = null;
  }

  sendLog({ type: 'SYSTEM', message: 'Content script stopped comprehensive capturing.' });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'PING') {
      sendResponse({ pong: true, ready: true });
      return false;
    } else if (request.type === 'START_RECORDING_CONTENT') {
      startCapturing({ sessionId: request.sessionId });
      sendResponse({
        success: true,
        message: 'Enhanced recording started in content script.',
        sessionId: request.sessionId
      });
      return false;
    } else if (request.type === 'STOP_RECORDING_CONTENT') {
      stopCapturing();
      sendResponse({
        success: true,
        message: 'Enhanced recording stopped in content script.',
        sessionId: request.sessionId
      });
      return false;
    } else if (request.type === 'START_VIDEO_RECORDING') {
      startVideoRecording().then(result => {
        sendResponse(result);
      }).catch(error => {
        sendResponse({ success: false, error: error.message });
      });
      return true; // Async response
    } else if (request.type === 'STOP_VIDEO_RECORDING') {
      stopVideoRecording().then(result => {
        sendResponse(result);
      }).catch(error => {
        sendResponse({ success: false, error: error.message });
      });
      return true; // Async response
    }
    return false; // Synchronous response
  });
}

// Initial overrides if the extension starts in a "recording" state (not typical for manual start)
// or handle state from storage if implementing persistence.
// For now, relies on popup message to start.
console.log('BugReplay content script loaded.');