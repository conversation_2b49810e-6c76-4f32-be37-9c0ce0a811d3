<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Video & Storage Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #1976d2;
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f0f0f0;
        }
        .logs {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🐛 BugReplay - Video & Storage Fixes Test</h1>
    
    <div class="info">
        <strong>Test Page for Video Recording & Storage Management Fixes</strong><br>
        This page tests the improvements made to handle video recording errors and storage quota issues.
    </div>

    <div class="test-section">
        <h2>📹 Video Recording Tests</h2>
        <p>Test the improved video recording functionality with better error handling and user guidance.</p>
        
        <button class="button" onclick="testVideoSupport()">Check Video Support</button>
        <button class="button" onclick="simulateVideoError()">Simulate Video Error</button>
        <button class="button" onclick="testSecureContext()">Check Secure Context</button>
        
        <div id="video-status" class="status">
            Click buttons above to test video recording capabilities...
        </div>
    </div>

    <div class="test-section">
        <h2>💾 Storage Management Tests</h2>
        <p>Test the storage quota management and cleanup functionality.</p>
        
        <button class="button" onclick="checkStorageUsage()">Check Storage Usage</button>
        <button class="button" onclick="simulateStorageQuota()">Simulate Storage Full</button>
        <button class="button" onclick="testStorageCleanup()">Test Cleanup</button>
        
        <div id="storage-status" class="status">
            Click buttons above to test storage management...
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Extension Integration Tests</h2>
        <p>Test the integration with the BugReplay extension.</p>
        
        <button class="button" onclick="testExtensionConnection()">Test Extension Connection</button>
        <button class="button" onclick="startRecordingTest()">Start Recording Test</button>
        <button class="button" onclick="stopRecordingTest()">Stop Recording Test</button>
        
        <div id="extension-status" class="status">
            Click buttons above to test extension integration...
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Test Logs</h2>
        <div id="test-logs" class="logs">
            Test logs will appear here...
        </div>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        // Logging utility
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`BugReplay Test: ${logEntry.trim()}`);
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = 'Test logs cleared...\n';
        }

        // Video recording tests
        function testVideoSupport() {
            log('Testing video recording support...');
            
            const statusDiv = document.getElementById('video-status');
            
            // Check secure context
            if (!window.isSecureContext) {
                statusDiv.innerHTML = '<div class="error">❌ Not in secure context (HTTPS required for video recording)</div>';
                log('Video recording requires HTTPS or localhost', 'error');
                return;
            }
            
            // Check getDisplayMedia support
            if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
                statusDiv.innerHTML = '<div class="error">❌ getDisplayMedia not supported in this browser</div>';
                log('getDisplayMedia not supported', 'error');
                return;
            }
            
            // Check MediaRecorder support
            if (!window.MediaRecorder) {
                statusDiv.innerHTML = '<div class="error">❌ MediaRecorder not supported in this browser</div>';
                log('MediaRecorder not supported', 'error');
                return;
            }
            
            statusDiv.innerHTML = '<div class="success">✅ Video recording is supported in this environment</div>';
            log('Video recording is fully supported', 'success');
        }

        function simulateVideoError() {
            log('Simulating video recording error...');
            
            const statusDiv = document.getElementById('video-status');
            const errors = [
                'NotAllowedError: Screen capture permission denied by user',
                'NotSupportedError: Screen capture not supported in this browser',
                'AbortError: Screen capture was cancelled by user',
                'Error: Screen sharing request timed out'
            ];
            
            const randomError = errors[Math.floor(Math.random() * errors.length)];
            statusDiv.innerHTML = `<div class="error">❌ Simulated Error: ${randomError}</div>`;
            log(`Simulated video error: ${randomError}`, 'error');
        }

        function testSecureContext() {
            log('Testing secure context...');
            
            const statusDiv = document.getElementById('video-status');
            const isSecure = window.isSecureContext;
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            
            if (isSecure) {
                statusDiv.innerHTML = `<div class="success">✅ Secure context (${protocol}//${hostname})</div>`;
                log(`Secure context confirmed: ${protocol}//${hostname}`, 'success');
            } else {
                statusDiv.innerHTML = `<div class="warning">⚠️ Not secure context (${protocol}//${hostname})</div>`;
                log(`Not in secure context: ${protocol}//${hostname}`, 'warning');
            }
        }

        // Storage management tests
        function checkStorageUsage() {
            log('Checking storage usage...');
            
            const statusDiv = document.getElementById('storage-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_STORAGE_USAGE' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Error: ${chrome.runtime.lastError.message}</div>`;
                        log(`Storage check error: ${chrome.runtime.lastError.message}`, 'error');
                        return;
                    }
                    
                    if (response && response.success) {
                        const usage = response.usage;
                        const usedMB = (usage.used / 1024 / 1024).toFixed(2);
                        const quotaMB = (usage.quota / 1024 / 1024).toFixed(2);
                        
                        let statusClass = 'info';
                        if (usage.percentage > 80) statusClass = 'error';
                        else if (usage.percentage > 60) statusClass = 'warning';
                        else statusClass = 'success';
                        
                        statusDiv.innerHTML = `
                            <div class="${statusClass}">
                                📊 Storage: ${usedMB}MB / ${quotaMB}MB (${usage.percentage}%)<br>
                                Sessions: ${usage.sessionCount}, Avg size: ${(usage.averageSessionSize / 1024).toFixed(1)}KB
                            </div>
                        `;
                        log(`Storage usage: ${usage.percentage}% (${usedMB}MB/${quotaMB}MB)`, 'info');
                    } else {
                        statusDiv.innerHTML = '<div class="error">❌ Failed to get storage usage</div>';
                        log('Failed to get storage usage', 'error');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                log('Chrome extension API not available', 'error');
            }
        }

        function simulateStorageQuota() {
            log('Simulating storage quota exceeded...');
            
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = '<div class="error">❌ Simulated: Storage quota exceeded. Cleanup required.</div>';
            log('Simulated storage quota exceeded error', 'warning');
        }

        function testStorageCleanup() {
            log('Testing storage cleanup...');
            
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = '<div class="info">🧹 Storage cleanup would remove old sessions to free space</div>';
            log('Storage cleanup test completed', 'info');
        }

        // Extension integration tests
        function testExtensionConnection() {
            log('Testing extension connection...');
            
            const statusDiv = document.getElementById('extension-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'PING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Extension not connected: ${chrome.runtime.lastError.message}</div>`;
                        log(`Extension connection failed: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        statusDiv.innerHTML = '<div class="success">✅ Extension connected successfully</div>';
                        log('Extension connection successful', 'success');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Chrome extension API not available</div>';
                log('Chrome extension API not available', 'error');
            }
        }

        function startRecordingTest() {
            log('Testing recording start...');
            
            const statusDiv = document.getElementById('extension-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'START_RECORDING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Recording start failed: ${chrome.runtime.lastError.message}</div>`;
                        log(`Recording start failed: ${chrome.runtime.lastError.message}`, 'error');
                    } else if (response && response.success) {
                        statusDiv.innerHTML = '<div class="success">✅ Recording started successfully</div>';
                        log('Recording started successfully', 'success');
                    } else {
                        statusDiv.innerHTML = `<div class="error">❌ Recording start failed: ${response?.message || 'Unknown error'}</div>`;
                        log(`Recording start failed: ${response?.message || 'Unknown error'}`, 'error');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                log('Extension not available for recording test', 'error');
            }
        }

        function stopRecordingTest() {
            log('Testing recording stop...');
            
            const statusDiv = document.getElementById('extension-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'STOP_RECORDING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Recording stop failed: ${chrome.runtime.lastError.message}</div>`;
                        log(`Recording stop failed: ${chrome.runtime.lastError.message}`, 'error');
                    } else if (response && response.success) {
                        statusDiv.innerHTML = '<div class="success">✅ Recording stopped successfully</div>';
                        log('Recording stopped successfully', 'success');
                    } else {
                        statusDiv.innerHTML = `<div class="error">❌ Recording stop failed: ${response?.message || 'Unknown error'}</div>`;
                        log(`Recording stop failed: ${response?.message || 'Unknown error'}`, 'error');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                log('Extension not available for recording test', 'error');
            }
        }

        // Initialize
        log('Test page loaded successfully');
        log('Ready to test BugReplay video and storage fixes');
    </script>
</body>
</html>
