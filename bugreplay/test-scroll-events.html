<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugReplay - Scroll Events Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .section {
            margin: 40px 0;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .tall-content {
            height: 800px;
            background: linear-gradient(180deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .scroll-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        .button.secondary {
            background: #2196F3;
        }
        .button.secondary:hover {
            background: #1976D2;
        }
        .logs {
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        h2 {
            color: #FFD700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .milestone {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        .content-block {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #FFD700;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        Scroll: 0%
    </div>

    <div class="container">
        <h1>🐛 BugReplay - Scroll Events Test Page</h1>
        
        <div class="instructions">
            <h2>📋 Test Instructions</h2>
            <ol>
                <li><strong>Start BugReplay recording</strong> before scrolling</li>
                <li><strong>Scroll down slowly</strong> through this page</li>
                <li><strong>Click buttons</strong> at different scroll positions</li>
                <li><strong>Scroll back up</strong> to test upward scrolling</li>
                <li><strong>Stop recording</strong> and check the bug report</li>
                <li><strong>Verify</strong> that scroll events appear in "Steps to Reproduce"</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Section 1 - Top of Page</h2>
            <p>This is the beginning of our scroll test. You should see this section first when the page loads.</p>
            <button class="button" onclick="logAction('Clicked button in Section 1')">Click Me - Section 1</button>
            <div class="content-block">
                <p>Content block 1: This is some sample content to make the page longer and provide scroll targets.</p>
            </div>
        </div>

        <div class="tall-content">
            <div>
                <h2>📜 Tall Content Area - Start</h2>
                <p>This is a tall content area designed to require scrolling. As you scroll through this section, BugReplay should capture your scroll events.</p>
                <button class="button secondary" onclick="logAction('Clicked button at start of tall content')">Button at Start</button>
            </div>
            
            <div style="text-align: center;">
                <h3>🔄 Middle of Tall Content</h3>
                <p>You're in the middle of the tall content area. Keep scrolling to test more scroll events.</p>
                <button class="button" onclick="logAction('Clicked middle button')">Middle Button</button>
            </div>
            
            <div>
                <h3>⬇️ End of Tall Content</h3>
                <p>You've reached the end of the tall content area. Great job scrolling!</p>
                <button class="button secondary" onclick="logAction('Clicked button at end of tall content')">Button at End</button>
            </div>
        </div>

        <div class="milestone">
            🎉 Milestone: 25% of page scrolled
        </div>

        <div class="section">
            <h2>🎯 Section 2 - Quarter Point</h2>
            <p>You've scrolled about 25% through the page. BugReplay should have captured several scroll events by now.</p>
            <button class="button" onclick="logAction('Clicked button in Section 2')">Click Me - Section 2</button>
            <div class="content-block">
                <p>Content block 2: More content to extend the page and create scroll opportunities.</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Section 3 - Mid Point</h2>
            <p>Halfway through the page! The scroll events should be accumulating in your BugReplay session.</p>
            <button class="button secondary" onclick="logAction('Clicked button in Section 3')">Click Me - Section 3</button>
            <div class="content-block">
                <p>Content block 3: Even more content to test scroll event capture at different page positions.</p>
            </div>
        </div>

        <div class="milestone">
            🎉 Milestone: 50% of page scrolled
        </div>

        <div class="section">
            <h2>🎯 Section 4 - Three Quarters</h2>
            <p>You're 75% through the page. Try scrolling up and down in this section to test bidirectional scrolling.</p>
            <button class="button" onclick="logAction('Clicked button in Section 4')">Click Me - Section 4</button>
            <div class="content-block">
                <p>Content block 4: This content helps test scroll events in the lower portion of the page.</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Section 5 - Near Bottom</h2>
            <p>Almost at the bottom! Test some final scroll events here.</p>
            <button class="button secondary" onclick="logAction('Clicked button in Section 5')">Click Me - Section 5</button>
            <div class="content-block">
                <p>Content block 5: Final content block to complete our scroll testing.</p>
            </div>
        </div>

        <div class="milestone">
            🎉 Milestone: 75% of page scrolled
        </div>

        <div class="section">
            <h2>🏁 Final Section - Bottom</h2>
            <p>You've reached the bottom of the page! Now try scrolling back up to test upward scroll events.</p>
            <button class="button" onclick="logAction('Clicked final button')">Final Button</button>
            <div class="content-block">
                <p>Final content: You've completed the scroll test. Check your BugReplay recording to see if scroll events were captured!</p>
            </div>
        </div>

        <div class="milestone">
            🎉 Test Complete: 100% of page scrolled
        </div>

        <!-- Test Results Section -->
        <div class="section">
            <h2>📊 Test Results</h2>
            <p>Use this section to verify scroll event capture:</p>
            <div id="test-logs" class="logs">
                Test logs will appear here...
            </div>
            <button class="button" onclick="clearLogs()">Clear Logs</button>
            <button class="button secondary" onclick="scrollToTop()">Scroll to Top</button>
        </div>
    </div>

    <script>
        let scrollEventCount = 0;
        
        // Update scroll indicator
        function updateScrollIndicator() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrollPercent = scrollHeight > 0 ? Math.round((scrollTop / scrollHeight) * 100) : 0;
            
            document.getElementById('scrollIndicator').textContent = `Scroll: ${scrollPercent}%`;
        }

        // Log actions for testing
        function logAction(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`BugReplay Test: ${message}`);
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = 'Test logs cleared...\n';
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
            logAction('Smooth scroll to top initiated');
        }

        // Track scroll events for testing
        let lastScrollTime = 0;
        window.addEventListener('scroll', function() {
            const now = Date.now();
            if (now - lastScrollTime < 250) return; // Match BugReplay throttling
            lastScrollTime = now;
            
            scrollEventCount++;
            updateScrollIndicator();
            
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrollPercent = scrollHeight > 0 ? Math.round((scrollTop / scrollHeight) * 100) : 0;
            
            logAction(`Scroll event #${scrollEventCount}: ${scrollPercent}% (${scrollTop}px)`);
        });

        // Initialize
        updateScrollIndicator();
        logAction('Scroll test page loaded - Start BugReplay recording and scroll!');
        
        // Add some helpful console messages
        console.log('BugReplay Scroll Test: Page loaded');
        console.log('BugReplay Scroll Test: Start recording and scroll to test event capture');
    </script>
</body>
</html>
